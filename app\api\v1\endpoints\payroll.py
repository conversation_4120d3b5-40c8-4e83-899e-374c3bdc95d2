from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Employee
from app.schemas.payroll import PayrollResponse, PayslipResponse, TaxFormResponse

router = APIRouter()

@router.get("/{employee_id}/payroll", response_model=PayrollResponse)
def get_employee_payroll(
    employee_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    return {
        "employee_id": employee.id,
        "salary": employee.salary,
        "pay_rate": "monthly",  # This could be from a separate payroll table
        "currency": "USD",
        "tax_rate": 0.25,  # This should come from a tax calculation service
        "deductions": []  # This should come from benefits and other deductions
    }

@router.get("/{employee_id}/payslips", response_model=List[PayslipResponse])
def get_employee_payslips(
    employee_id: int,
    skip: int = 0,
    limit: int = 12,  # Default to last 12 months
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    # In a real implementation, this would query a payslips table
    return []

@router.get("/{employee_id}/payslips/{payslip_id}")
def get_payslip_pdf(
    employee_id: int,
    payslip_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # In a real implementation, this would generate or retrieve a PDF
    raise HTTPException(status_code=501, detail="PDF generation not implemented")

@router.get("/{employee_id}/tax-forms", response_model=List[TaxFormResponse])
def get_employee_tax_forms(
    employee_id: int,
    year: int = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    # In a real implementation, this would query a tax_forms table
    return []
