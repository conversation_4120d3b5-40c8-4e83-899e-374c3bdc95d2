from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models.employee import TimesheetEntry, PunchSession, Employee
from app.core.timesheet_utils import calculate_hours_worked, validate_timesheet_entry
from app.schemas.timesheet import TimesheetStatus, EntryMethod
import logging

logger = logging.getLogger(__name__)


class TimesheetService:
    """Service layer for timesheet business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_employee_timesheet_summary(
        self, 
        employee_id: int, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Get comprehensive timesheet summary for an employee"""
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.employee_id == employee_id,
            TimesheetEntry.date >= start_date,
            TimesheetEntry.date <= end_date
        ).all()
        
        total_hours = sum(entry.hours_worked for entry in entries)
        days_worked = len(set(entry.date for entry in entries))
        
        # Group by entry method
        method_breakdown = {}
        for entry in entries:
            method = entry.entry_method
            if method not in method_breakdown:
                method_breakdown[method] = {'count': 0, 'hours': Decimal('0.00')}
            method_breakdown[method]['count'] += 1
            method_breakdown[method]['hours'] += entry.hours_worked
        
        # Group by status
        status_breakdown = {}
        for entry in entries:
            status = entry.status
            if status not in status_breakdown:
                status_breakdown[status] = {'count': 0, 'hours': Decimal('0.00')}
            status_breakdown[status]['count'] += 1
            status_breakdown[status]['hours'] += entry.hours_worked
        
        # Calculate overtime (assuming 40 hours per week)
        weeks_in_period = (end_date - start_date).days / 7
        expected_hours = weeks_in_period * 40
        overtime_hours = max(Decimal('0.00'), total_hours - Decimal(str(expected_hours)))
        
        return {
            'employee_id': employee_id,
            'period': {'start_date': start_date, 'end_date': end_date},
            'total_hours': total_hours,
            'days_worked': days_worked,
            'average_daily_hours': total_hours / days_worked if days_worked > 0 else Decimal('0.00'),
            'overtime_hours': overtime_hours,
            'method_breakdown': method_breakdown,
            'status_breakdown': status_breakdown,
            'entries_count': len(entries)
        }
    
    def detect_timesheet_anomalies(
        self, 
        employee_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Detect potential anomalies in timesheet data"""
        anomalies = []
        
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.employee_id == employee_id,
            TimesheetEntry.date >= start_date,
            TimesheetEntry.date <= end_date
        ).order_by(TimesheetEntry.date, TimesheetEntry.start_time).all()
        
        for entry in entries:
            # Check for excessive hours (more than 12 hours per day)
            if entry.hours_worked > 12:
                anomalies.append({
                    'type': 'excessive_hours',
                    'entry_id': entry.id,
                    'date': entry.date,
                    'hours': entry.hours_worked,
                    'message': f'Excessive hours worked: {entry.hours_worked} hours'
                })
            
            # Check for very short shifts (less than 1 hour)
            if entry.hours_worked < 1:
                anomalies.append({
                    'type': 'short_shift',
                    'entry_id': entry.id,
                    'date': entry.date,
                    'hours': entry.hours_worked,
                    'message': f'Very short shift: {entry.hours_worked} hours'
                })
            
            # Check for late night work (after 11 PM or before 5 AM)
            if entry.start_time.hour >= 23 or entry.start_time.hour < 5:
                anomalies.append({
                    'type': 'late_night_work',
                    'entry_id': entry.id,
                    'date': entry.date,
                    'start_time': entry.start_time,
                    'message': f'Late night work starting at {entry.start_time.strftime("%H:%M")}'
                })
            
            # Check for weekend work
            if entry.date.weekday() >= 5:  # Saturday = 5, Sunday = 6
                anomalies.append({
                    'type': 'weekend_work',
                    'entry_id': entry.id,
                    'date': entry.date,
                    'hours': entry.hours_worked,
                    'message': f'Weekend work on {entry.date.strftime("%A")}'
                })
        
        # Check for missing days (gaps in work schedule)
        if len(entries) > 1:
            for i in range(1, len(entries)):
                prev_date = entries[i-1].date
                curr_date = entries[i].date
                gap_days = (curr_date - prev_date).days
                
                if gap_days > 3 and prev_date.weekday() < 5:  # More than 3 days gap on weekdays
                    anomalies.append({
                        'type': 'work_gap',
                        'start_date': prev_date,
                        'end_date': curr_date,
                        'gap_days': gap_days,
                        'message': f'{gap_days} day gap between {prev_date} and {curr_date}'
                    })
        
        return anomalies
    
    def get_overlapping_entries(
        self, 
        employee_id: int, 
        start_time: datetime, 
        end_time: datetime,
        exclude_entry_id: Optional[int] = None
    ) -> List[TimesheetEntry]:
        """Get all timesheet entries that overlap with the given time range"""
        query = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.employee_id == employee_id,
            TimesheetEntry.date == start_time.date(),
            or_(
                and_(
                    TimesheetEntry.start_time <= start_time,
                    TimesheetEntry.end_time > start_time
                ),
                and_(
                    TimesheetEntry.start_time < end_time,
                    TimesheetEntry.end_time >= end_time
                ),
                and_(
                    TimesheetEntry.start_time >= start_time,
                    TimesheetEntry.end_time <= end_time
                )
            )
        )
        
        if exclude_entry_id:
            query = query.filter(TimesheetEntry.id != exclude_entry_id)
        
        return query.all()
    
    def calculate_weekly_totals(
        self, 
        employee_id: int, 
        week_start: date
    ) -> Dict[str, Any]:
        """Calculate weekly totals for an employee"""
        week_end = week_start + timedelta(days=6)
        
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.employee_id == employee_id,
            TimesheetEntry.date >= week_start,
            TimesheetEntry.date <= week_end
        ).all()
        
        daily_totals = {}
        total_hours = Decimal('0.00')
        
        for entry in entries:
            day_key = entry.date.strftime('%Y-%m-%d')
            if day_key not in daily_totals:
                daily_totals[day_key] = Decimal('0.00')
            daily_totals[day_key] += entry.hours_worked
            total_hours += entry.hours_worked
        
        # Calculate overtime (assuming 40 hours per week)
        regular_hours = min(total_hours, Decimal('40.00'))
        overtime_hours = max(Decimal('0.00'), total_hours - Decimal('40.00'))
        
        return {
            'week_start': week_start,
            'week_end': week_end,
            'daily_totals': daily_totals,
            'total_hours': total_hours,
            'regular_hours': regular_hours,
            'overtime_hours': overtime_hours,
            'days_worked': len(daily_totals)
        }
    
    def approve_timesheet_entries(
        self, 
        entry_ids: List[int], 
        approver_id: int
    ) -> Dict[str, Any]:
        """Approve multiple timesheet entries"""
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.id.in_(entry_ids)
        ).all()
        
        if len(entries) != len(entry_ids):
            missing_ids = set(entry_ids) - set(entry.id for entry in entries)
            raise ValueError(f"Entries not found: {missing_ids}")
        
        approved_count = 0
        for entry in entries:
            if entry.status == TimesheetStatus.SUBMITTED:
                entry.status = TimesheetStatus.APPROVED
                approved_count += 1
        
        self.db.commit()
        
        return {
            'approved_count': approved_count,
            'total_entries': len(entries),
            'approver_id': approver_id
        }
    
    def reject_timesheet_entries(
        self, 
        entry_ids: List[int], 
        rejection_reason: str,
        approver_id: int
    ) -> Dict[str, Any]:
        """Reject multiple timesheet entries"""
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.id.in_(entry_ids)
        ).all()
        
        if len(entries) != len(entry_ids):
            missing_ids = set(entry_ids) - set(entry.id for entry in entries)
            raise ValueError(f"Entries not found: {missing_ids}")
        
        rejected_count = 0
        for entry in entries:
            if entry.status == TimesheetStatus.SUBMITTED:
                entry.status = TimesheetStatus.REJECTED
                entry.notes = (entry.notes or "") + f" [REJECTED: {rejection_reason}]"
                rejected_count += 1
        
        self.db.commit()
        
        return {
            'rejected_count': rejected_count,
            'total_entries': len(entries),
            'rejection_reason': rejection_reason,
            'approver_id': approver_id
        }
    
    def get_pending_approvals(self, limit: int = 100) -> List[TimesheetEntry]:
        """Get timesheet entries pending approval"""
        return self.db.query(TimesheetEntry).filter(
            TimesheetEntry.status == TimesheetStatus.SUBMITTED
        ).order_by(TimesheetEntry.created_at).limit(limit).all()
    
    def auto_submit_completed_entries(self, days_old: int = 7) -> int:
        """Auto-submit timesheet entries that are older than specified days"""
        cutoff_date = date.today() - timedelta(days=days_old)
        
        entries = self.db.query(TimesheetEntry).filter(
            TimesheetEntry.date <= cutoff_date,
            TimesheetEntry.status == "draft"  # Assuming draft status exists
        ).all()
        
        submitted_count = 0
        for entry in entries:
            entry.status = TimesheetStatus.SUBMITTED
            submitted_count += 1
        
        self.db.commit()
        return submitted_count
