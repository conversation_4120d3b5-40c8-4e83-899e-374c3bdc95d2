from pydantic import BaseModel
from datetime import datetime, date
from typing import Optional

class BenefitBase(BaseModel):
    name: str
    description: str
    type: str

class BenefitResponse(BenefitBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class EmployeeBenefitBase(BaseModel):
    benefit_id: int

class EmployeeBenefitCreate(EmployeeBenefitBase):
    pass

class EmployeeBenefitResponse(EmployeeBenefitBase):
    id: int
    employee_id: int
    enrollment_date: date
    status: str
    created_at: datetime
    updated_at: datetime
    benefit: BenefitResponse

    class Config:
        from_attributes = True
