from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Document
from app.schemas.document import DocumentCreate, DocumentResponse

router = APIRouter()

@router.get("/{employee_id}/documents", response_model=List[DocumentResponse])
def list_employee_documents(
    employee_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    documents = db.query(Document).filter(
        Document.employee_id == employee_id
    ).offset(skip).limit(limit).all()
    return documents

@router.post("/{employee_id}/documents", response_model=DocumentResponse)
async def upload_document(
    employee_id: int,
    title: str,
    document_type: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # In a real implementation, save the file and store its path
    file_path = f"documents/{employee_id}/{document_type}/{file.filename}"
    
    document = Document(
        employee_id=employee_id,
        title=title,
        document_type=document_type,
        file_path=file_path
    )
    db.add(document)
    db.commit()
    db.refresh(document)
    return document

@router.get("/documents/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    document = db.query(Document).filter(Document.id == doc_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    return document

@router.delete("/documents/{doc_id}")
async def delete_document(
    doc_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    document = db.query(Document).filter(Document.id == doc_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # In a real implementation, delete the actual file
    db.delete(document)
    db.commit()
    return {"message": "Document deleted successfully"}
