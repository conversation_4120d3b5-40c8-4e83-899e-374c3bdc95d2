from datetime import datetime, timedelta, date
from decimal import Decimal
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from app.models.employee import TimesheetEntry, PunchSession


def calculate_hours_worked(start_time: datetime, end_time: datetime, break_duration: int = 0) -> Decimal:
    """Calculate hours worked between start and end time, minus break duration"""
    if end_time <= start_time:
        return Decimal('0.00')
    
    total_minutes = (end_time - start_time).total_seconds() / 60
    work_minutes = total_minutes - break_duration
    
    if work_minutes <= 0:
        return Decimal('0.00')
    
    hours = work_minutes / 60
    return Decimal(str(round(hours, 2)))


def check_time_overlap(
    start_time: datetime, 
    end_time: datetime, 
    employee_id: int, 
    db: Session,
    exclude_entry_id: Optional[int] = None
) -> List[dict]:
    """Check for overlapping timesheet entries for an employee"""
    overlaps = []
    
    # Query existing entries for the same date
    query = db.query(TimesheetEntry).filter(
        TimesheetEntry.employee_id == employee_id,
        TimesheetEntry.date == start_time.date()
    )
    
    if exclude_entry_id:
        query = query.filter(TimesheetEntry.id != exclude_entry_id)
    
    existing_entries = query.all()
    
    for entry in existing_entries:
        # Check if times overlap
        if (start_time < entry.end_time and end_time > entry.start_time):
            overlaps.append({
                'entry_id': entry.id,
                'existing_start': entry.start_time,
                'existing_end': entry.end_time,
                'overlap_start': max(start_time, entry.start_time),
                'overlap_end': min(end_time, entry.end_time)
            })
    
    return overlaps


def validate_punch_session(employee_id: int, db: Session) -> Tuple[bool, Optional[str], Optional[PunchSession]]:
    """Validate if employee can punch in/out"""
    # Check for active sessions
    active_session = db.query(PunchSession).filter(
        PunchSession.employee_id == employee_id,
        PunchSession.is_active == True
    ).first()
    
    return True, None, active_session


def auto_punch_out_stale_sessions(db: Session, hours_threshold: int = 24):
    """Automatically punch out sessions that have been active too long"""
    cutoff_time = datetime.utcnow() - timedelta(hours=hours_threshold)
    
    stale_sessions = db.query(PunchSession).filter(
        PunchSession.is_active == True,
        PunchSession.punch_in_time < cutoff_time
    ).all()
    
    for session in stale_sessions:
        # Auto punch out with a note
        session.punch_out_time = session.punch_in_time + timedelta(hours=8)  # Assume 8-hour day
        session.is_active = False
        session.session_notes = (session.session_notes or "") + " [AUTO PUNCH OUT - STALE SESSION]"
        
        # Create timesheet entry
        create_timesheet_from_session(session, db)
    
    db.commit()
    return len(stale_sessions)


def create_timesheet_from_session(session: PunchSession, db: Session) -> TimesheetEntry:
    """Create a timesheet entry from a completed punch session"""
    if not session.punch_out_time:
        raise ValueError("Cannot create timesheet entry from active session")
    
    hours_worked = calculate_hours_worked(
        session.punch_in_time, 
        session.punch_out_time
    )
    
    timesheet_entry = TimesheetEntry(
        employee_id=session.employee_id,
        date=session.punch_in_time.date(),
        start_time=session.punch_in_time,
        end_time=session.punch_out_time,
        hours_worked=hours_worked,
        entry_method="punch_clock",
        notes=session.session_notes
    )
    
    db.add(timesheet_entry)
    return timesheet_entry


def generate_date_range(start_date: date, end_date: date, exclude_weekends: bool = True) -> List[date]:
    """Generate a list of dates between start and end date"""
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        if not exclude_weekends or current_date.weekday() < 5:  # Monday = 0, Sunday = 6
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    return dates


def validate_timesheet_entry(
    start_time: datetime,
    end_time: datetime,
    break_duration: int,
    employee_id: int,
    db: Session,
    exclude_entry_id: Optional[int] = None
) -> List[str]:
    """Validate a timesheet entry and return list of errors"""
    errors = []
    
    # Basic time validation
    if end_time <= start_time:
        errors.append("End time must be after start time")
    
    # Break duration validation
    if break_duration < 0:
        errors.append("Break duration cannot be negative")
    
    total_minutes = (end_time - start_time).total_seconds() / 60
    if break_duration >= total_minutes:
        errors.append("Break duration cannot be longer than total work time")
    
    # Check for overlaps
    overlaps = check_time_overlap(start_time, end_time, employee_id, db, exclude_entry_id)
    if overlaps:
        errors.append(f"Time overlaps with {len(overlaps)} existing entries")
    
    # Maximum hours per day check (configurable)
    hours_worked = calculate_hours_worked(start_time, end_time, break_duration)
    if hours_worked > 24:
        errors.append("Cannot work more than 24 hours in a day")
    
    return errors


def get_weekly_summary(employee_id: int, week_start: date, db: Session) -> dict:
    """Get weekly timesheet summary for an employee"""
    week_end = week_start + timedelta(days=6)
    
    entries = db.query(TimesheetEntry).filter(
        TimesheetEntry.employee_id == employee_id,
        TimesheetEntry.date >= week_start,
        TimesheetEntry.date <= week_end
    ).all()
    
    total_hours = sum(entry.hours_worked for entry in entries)
    days_worked = len(set(entry.date for entry in entries))
    
    return {
        'week_start': week_start,
        'week_end': week_end,
        'total_hours': total_hours,
        'days_worked': days_worked,
        'entries_count': len(entries),
        'average_daily_hours': total_hours / days_worked if days_worked > 0 else 0
    }
