#!/usr/bin/env python3
"""
Test script to verify all user management endpoints work correctly.
"""

import requests
import json
from typing import Optional

BASE_URL = "http://127.0.0.1:8000/api/v1"

class UserAPITester:
    def __init__(self):
        self.access_token: Optional[str] = None
        self.test_user_id: Optional[int] = None
        
    def login(self, email: str, password: str) -> bool:
        """Login and get access token."""
        try:
            response = requests.post(
                f"{BASE_URL}/users/token",
                data={"username": email, "password": password}
            )
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                print(f"✓ Login successful for {email}")
                return True
            else:
                print(f"✗ Login failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Login error: {e}")
            return False
    
    def get_headers(self):
        """Get authorization headers."""
        if not self.access_token:
            raise Exception("Not logged in")
        return {"Authorization": f"Bearer {self.access_token}"}
    
    def create_user(self, user_data: dict) -> Optional[int]:
        """Create a new user and return user ID."""
        try:
            response = requests.post(f"{BASE_URL}/users/users/", json=user_data)
            if response.status_code == 200:
                data = response.json()
                user_id = data["id"]
                print(f"✓ User created successfully: ID {user_id}")
                return user_id
            else:
                print(f"✗ User creation failed: {response.text}")
                return None
        except Exception as e:
            print(f"✗ User creation error: {e}")
            return None
    
    def get_users(self, skip: int = 0, limit: int = 10) -> bool:
        """Test GET /users/ endpoint."""
        try:
            response = requests.get(
                f"{BASE_URL}/users/users/",
                params={"skip": skip, "limit": limit},
                headers=self.get_headers()
            )
            if response.status_code == 200:
                users = response.json()
                print(f"✓ Retrieved {len(users)} users")
                return True
            else:
                print(f"✗ Get users failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Get users error: {e}")
            return False
    
    def get_user(self, user_id: int) -> bool:
        """Test GET /users/{user_id} endpoint."""
        try:
            response = requests.get(
                f"{BASE_URL}/users/users/{user_id}",
                headers=self.get_headers()
            )
            if response.status_code == 200:
                user = response.json()
                print(f"✓ Retrieved user: {user['email']}")
                return True
            else:
                print(f"✗ Get user failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Get user error: {e}")
            return False
    
    def update_user(self, user_id: int, update_data: dict) -> bool:
        """Test PUT /users/{user_id} endpoint."""
        try:
            response = requests.put(
                f"{BASE_URL}/users/users/{user_id}",
                json=update_data,
                headers=self.get_headers()
            )
            if response.status_code == 200:
                user = response.json()
                print(f"✓ User updated successfully: {user['email']}")
                return True
            else:
                print(f"✗ Update user failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Update user error: {e}")
            return False
    
    def delete_user(self, user_id: int) -> bool:
        """Test DELETE /users/{user_id} endpoint (soft delete)."""
        try:
            response = requests.delete(
                f"{BASE_URL}/users/users/{user_id}",
                headers=self.get_headers()
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✓ User soft deleted: {result['message']}")
                return True
            else:
                print(f"✗ Delete user failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Delete user error: {e}")
            return False
    
    def reactivate_user(self, user_id: int) -> bool:
        """Test PATCH /users/{user_id}/reactivate endpoint."""
        try:
            response = requests.patch(
                f"{BASE_URL}/users/users/{user_id}/reactivate",
                headers=self.get_headers()
            )
            if response.status_code == 200:
                user = response.json()
                print(f"✓ User reactivated: {user['email']}")
                return True
            else:
                print(f"✗ Reactivate user failed: {response.text}")
                return False
        except Exception as e:
            print(f"✗ Reactivate user error: {e}")
            return False

def main():
    """Run comprehensive tests for user management endpoints."""
    print("Testing User Management Endpoints")
    print("=" * 50)
    
    tester = UserAPITester()
    
    # Test data
    admin_user = {
        "email": "<EMAIL>",
        "password": "adminpassword123",
        "first_name": "Admin",
        "last_name": "User"
    }
    
    test_user = {
        "email": "<EMAIL>", 
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    # Step 1: Create admin user (if not exists)
    print("\n1. Creating admin user...")
    admin_id = tester.create_user(admin_user)
    
    # Step 2: Login as admin
    print("\n2. Logging in as admin...")
    if not tester.login(admin_user["email"], admin_user["password"]):
        print("Cannot proceed without admin login")
        return
    
    # Step 3: Create test user
    print("\n3. Creating test user...")
    test_user_id = tester.create_user(test_user)
    if not test_user_id:
        print("Cannot proceed without test user")
        return
    
    # Step 4: Test GET endpoints
    print("\n4. Testing GET endpoints...")
    tester.get_users()
    tester.get_user(test_user_id)
    
    # Step 5: Test PUT endpoint
    print("\n5. Testing PUT endpoint...")
    update_data = {
        "first_name": "Updated",
        "last_name": "TestUser"
    }
    tester.update_user(test_user_id, update_data)
    
    # Step 6: Test DELETE endpoint (soft delete)
    print("\n6. Testing DELETE endpoint (soft delete)...")
    tester.delete_user(test_user_id)
    
    # Step 7: Test reactivate endpoint
    print("\n7. Testing reactivate endpoint...")
    tester.reactivate_user(test_user_id)
    
    print("\n" + "=" * 50)
    print("✅ All user management endpoint tests completed!")
    print("Check the output above for any failures.")

if __name__ == "__main__":
    main()
