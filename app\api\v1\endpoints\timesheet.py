from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Timesheet, LeaveRequest
from app.schemas.timesheet import (
    TimesheetCreate,
    TimesheetUpdate,
    TimesheetResponse,
    LeaveRequestCreate,
    LeaveRequestUpdate,
    LeaveRequestResponse
)

router = APIRouter()

# Timesheet endpoints
@router.get("/{employee_id}/timesheets", response_model=List[TimesheetResponse])
def get_employee_timesheets(
    employee_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    timesheets = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id
    ).offset(skip).limit(limit).all()
    return timesheets

@router.post("/{employee_id}/timesheets", response_model=TimesheetResponse)
def create_timesheet(
    employee_id: int,
    timesheet: TimesheetCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_timesheet = Timesheet(**timesheet.dict(), employee_id=employee_id)
    db.add(db_timesheet)
    db.commit()
    db.refresh(db_timesheet)
    return db_timesheet

@router.get("/{employee_id}/timesheets/{timesheet_id}", response_model=TimesheetResponse)
def get_timesheet(
    employee_id: int,
    timesheet_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    timesheet = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id,
        Timesheet.id == timesheet_id
    ).first()
    if not timesheet:
        raise HTTPException(status_code=404, detail="Timesheet not found")
    return timesheet

@router.put("/{employee_id}/timesheets/{timesheet_id}", response_model=TimesheetResponse)
def update_timesheet(
    employee_id: int,
    timesheet_id: int,
    timesheet_update: TimesheetUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_timesheet = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id,
        Timesheet.id == timesheet_id
    ).first()
    if not db_timesheet:
        raise HTTPException(status_code=404, detail="Timesheet not found")
    
    for field, value in timesheet_update.dict().items():
        setattr(db_timesheet, field, value)
    
    db.commit()
    db.refresh(db_timesheet)
    return db_timesheet

# Leave request endpoints
@router.post("/leave/request", response_model=LeaveRequestResponse)
def create_leave_request(
    leave_request: LeaveRequestCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_leave_request = LeaveRequest(**leave_request.dict())
    db.add(db_leave_request)
    db.commit()
    db.refresh(db_leave_request)
    return db_leave_request

@router.get("/leave/requests", response_model=List[LeaveRequestResponse])
def get_leave_requests(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if user has admin/manager privileges here
    leave_requests = db.query(LeaveRequest).offset(skip).limit(limit).all()
    return leave_requests

@router.patch("/leave/requests/{request_id}", response_model=LeaveRequestResponse)
def update_leave_request(
    request_id: int,
    leave_request: LeaveRequestUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if user has admin/manager privileges here
    db_leave_request = db.query(LeaveRequest).filter(
        LeaveRequest.id == request_id
    ).first()
    if not db_leave_request:
        raise HTTPException(status_code=404, detail="Leave request not found")
    
    for field, value in leave_request.dict(exclude_unset=True).items():
        setattr(db_leave_request, field, value)
    
    db.commit()
    db.refresh(db_leave_request)
    return db_leave_request
