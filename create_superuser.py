#!/usr/bin/env python3
"""
Script to create a superuser or make an existing user a superuser.
"""

import sys
sys.path.append('.')

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash

def create_superuser(email: str, password: str, first_name: str = "Super", last_name: str = "User"):
    """Create a superuser or update existing user to superuser."""
    db = SessionLocal()
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        
        if existing_user:
            # Make existing user a superuser
            existing_user.is_superuser = True
            existing_user.is_active = True
            print(f"✓ Made {email} a superuser")
        else:
            # Create new superuser
            superuser = User(
                email=email,
                hashed_password=get_password_hash(password),
                first_name=first_name,
                last_name=last_name,
                is_active=True,
                is_superuser=True
            )
            db.add(superuser)
            print(f"✓ Created superuser {email}")
        
        db.commit()
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    # Create/update admin user to be superuser
    success = create_superuser(
        email="<EMAIL>",
        password="adminpassword123",
        first_name="Admin",
        last_name="User"
    )
    
    if success:
        print("Superuser setup completed successfully!")
    else:
        print("Failed to setup superuser!")
        sys.exit(1)
