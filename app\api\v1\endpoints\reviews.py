from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Review
from app.schemas.review import (
    ReviewCreate,
    ReviewUpdate,
    ReviewResponse,
    GoalCreate,
    GoalResponse
)

router = APIRouter()

@router.get("/{employee_id}/reviews", response_model=List[ReviewResponse])
def get_employee_reviews(
    employee_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    reviews = db.query(Review).filter(
        Review.employee_id == employee_id
    ).offset(skip).limit(limit).all()
    return reviews

@router.post("/{employee_id}/reviews", response_model=ReviewResponse)
def create_review(
    employee_id: int,
    review: ReviewCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_review = Review(**review.dict(), employee_id=employee_id)
    db.add(db_review)
    db.commit()
    db.refresh(db_review)
    return db_review

@router.get("/reviews/{review_id}", response_model=ReviewResponse)
def get_review(
    review_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    review = db.query(Review).filter(Review.id == review_id).first()
    if not review:
        raise HTTPException(status_code=404, detail="Review not found")
    return review

@router.put("/reviews/{review_id}", response_model=ReviewResponse)
def update_review(
    review_id: int,
    review_update: ReviewUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_review = db.query(Review).filter(Review.id == review_id).first()
    if not db_review:
        raise HTTPException(status_code=404, detail="Review not found")

    for field, value in review_update.dict(exclude_unset=True).items():
        setattr(db_review, field, value)
    
    db.commit()
    db.refresh(db_review)
    return db_review

@router.get("/{employee_id}/goals", response_model=List[GoalResponse])
def get_employee_goals(
    employee_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # In a real implementation, this would query a goals table
    return []

@router.post("/{employee_id}/goals", response_model=GoalResponse)
def create_goal(
    employee_id: int,
    goal: GoalCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # In a real implementation, this would create a new goal
    raise HTTPException(status_code=501, detail="Goals feature not implemented yet")
