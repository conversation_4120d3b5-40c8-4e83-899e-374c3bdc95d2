from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Benefit, EmployeeBenefit
from app.schemas.benefit import (
    BenefitResponse,
    EmployeeBenefitCreate,
    EmployeeBenefitResponse
)

router = APIRouter()

@router.get("/benefits", response_model=List[BenefitResponse])
def list_benefits(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    benefits = db.query(Benefit).filter(
        Benefit.is_active == True
    ).offset(skip).limit(limit).all()
    return benefits

@router.get("/{employee_id}/benefits", response_model=List[EmployeeBenefitResponse])
def get_employee_benefits(
    employee_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    benefits = db.query(EmployeeBenefit).filter(
        EmployeeBenefit.employee_id == employee_id
    ).all()
    return benefits

@router.post("/{employee_id}/benefits/enroll", response_model=EmployeeBenefitResponse)
def enroll_in_benefit(
    employee_id: int,
    benefit_enrollment: EmployeeBenefitCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if benefit exists and is active
    benefit = db.query(Benefit).filter(
        Benefit.id == benefit_enrollment.benefit_id,
        Benefit.is_active == True
    ).first()
    if not benefit:
        raise HTTPException(status_code=404, detail="Benefit not found or inactive")

    # Check if employee is already enrolled
    existing_enrollment = db.query(EmployeeBenefit).filter(
        EmployeeBenefit.employee_id == employee_id,
        EmployeeBenefit.benefit_id == benefit_enrollment.benefit_id,
        EmployeeBenefit.status == "active"
    ).first()
    if existing_enrollment:
        raise HTTPException(
            status_code=400,
            detail="Employee is already enrolled in this benefit"
        )

    # Create new enrollment
    db_enrollment = EmployeeBenefit(
        **benefit_enrollment.dict(),
        employee_id=employee_id,
        status="active"
    )
    db.add(db_enrollment)
    db.commit()
    db.refresh(db_enrollment)
    return db_enrollment
