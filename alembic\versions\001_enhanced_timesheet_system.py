"""Enhanced timesheet system with multiple input methods

Revision ID: 001_enhanced_timesheet
Revises: 
Create Date: 2024-08-14 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '001_enhanced_timesheet'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create timesheet_entries table
    op.create_table('timesheet_entries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=True),
        sa.Column('date', sa.Date(), nullable=True),
        sa.Column('start_time', sa.DateTime(), nullable=True),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('break_duration', sa.Integer(), nullable=True),
        sa.Column('hours_worked', sa.Numeric(precision=4, scale=2), nullable=True),
        sa.Column('project_code', sa.String(), nullable=True),
        sa.Column('task_description', sa.Text(), nullable=True),
        sa.Column('entry_method', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_timesheet_entries_id'), 'timesheet_entries', ['id'], unique=False)

    # Create punch_sessions table
    op.create_table('punch_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=True),
        sa.Column('punch_in_time', sa.DateTime(), nullable=True),
        sa.Column('punch_out_time', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('device_info', sa.String(), nullable=True),
        sa.Column('session_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_punch_sessions_id'), 'punch_sessions', ['id'], unique=False)

    # Create import_jobs table
    op.create_table('import_jobs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=True),
        sa.Column('filename', sa.String(), nullable=True),
        sa.Column('file_path', sa.String(), nullable=True),
        sa.Column('total_records', sa.Integer(), nullable=True),
        sa.Column('processed_records', sa.Integer(), nullable=True),
        sa.Column('successful_records', sa.Integer(), nullable=True),
        sa.Column('failed_records', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('error_details', sa.JSON(), nullable=True),
        sa.Column('preview_data', sa.JSON(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_import_jobs_id'), 'import_jobs', ['id'], unique=False)

    # Add indexes for better performance
    op.create_index('ix_timesheet_entries_employee_date', 'timesheet_entries', ['employee_id', 'date'])
    op.create_index('ix_timesheet_entries_status', 'timesheet_entries', ['status'])
    op.create_index('ix_timesheet_entries_entry_method', 'timesheet_entries', ['entry_method'])
    
    op.create_index('ix_punch_sessions_employee_active', 'punch_sessions', ['employee_id', 'is_active'])
    op.create_index('ix_punch_sessions_punch_in_time', 'punch_sessions', ['punch_in_time'])
    
    op.create_index('ix_import_jobs_employee_status', 'import_jobs', ['employee_id', 'status'])
    op.create_index('ix_import_jobs_created_at', 'import_jobs', ['created_at'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('ix_import_jobs_created_at', table_name='import_jobs')
    op.drop_index('ix_import_jobs_employee_status', table_name='import_jobs')
    op.drop_index('ix_punch_sessions_punch_in_time', table_name='punch_sessions')
    op.drop_index('ix_punch_sessions_employee_active', table_name='punch_sessions')
    op.drop_index('ix_timesheet_entries_entry_method', table_name='timesheet_entries')
    op.drop_index('ix_timesheet_entries_status', table_name='timesheet_entries')
    op.drop_index('ix_timesheet_entries_employee_date', table_name='timesheet_entries')
    
    # Drop tables
    op.drop_index(op.f('ix_import_jobs_id'), table_name='import_jobs')
    op.drop_table('import_jobs')
    
    op.drop_index(op.f('ix_punch_sessions_id'), table_name='punch_sessions')
    op.drop_table('punch_sessions')
    
    op.drop_index(op.f('ix_timesheet_entries_id'), table_name='timesheet_entries')
    op.drop_table('timesheet_entries')
