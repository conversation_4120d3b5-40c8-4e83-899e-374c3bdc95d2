import pandas as pd
import os
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
from datetime import datetime, date
from decimal import Decimal
import logging
from sqlalchemy.orm import Session
from app.models.employee import TimesheetEntry, ImportJob
from app.schemas.timesheet import CSVValidationError, CSVPreviewData, ImportJobStatus
from app.core.timesheet_utils import calculate_hours_worked, validate_timesheet_entry
from app.core.config import settings

logger = logging.getLogger(__name__)

# Expected CSV columns
REQUIRED_COLUMNS = ['employee_id', 'date', 'start_time', 'end_time']
OPTIONAL_COLUMNS = ['break_duration', 'project_code', 'task_description', 'notes']
ALL_COLUMNS = REQUIRED_COLUMNS + OPTIONAL_COLUMNS


def validate_csv_format(file_path: str) -> Tuple[bool, List[CSVValidationError], Optional[pd.DataFrame]]:
    """Validate CSV file format and return errors"""
    errors = []
    
    try:
        # Read CSV file
        df = pd.read_csv(file_path)
        
        # Check if file is empty
        if df.empty:
            errors.append(CSVValidationError(
                row=0,
                column="file",
                error="CSV file is empty",
                value=None
            ))
            return False, errors, None
        
        # Check required columns
        missing_columns = [col for col in REQUIRED_COLUMNS if col not in df.columns]
        if missing_columns:
            errors.append(CSVValidationError(
                row=0,
                column="headers",
                error=f"Missing required columns: {', '.join(missing_columns)}",
                value=df.columns.tolist()
            ))
        
        # Validate data types and values
        for index, row in df.iterrows():
            row_num = index + 2  # +2 because pandas is 0-indexed and we skip header
            
            # Validate employee_id
            if pd.isna(row.get('employee_id')):
                errors.append(CSVValidationError(
                    row=row_num,
                    column="employee_id",
                    error="Employee ID is required",
                    value=row.get('employee_id')
                ))
            elif not str(row.get('employee_id')).isdigit():
                errors.append(CSVValidationError(
                    row=row_num,
                    column="employee_id",
                    error="Employee ID must be a number",
                    value=row.get('employee_id')
                ))
            
            # Validate date
            try:
                if pd.isna(row.get('date')):
                    errors.append(CSVValidationError(
                        row=row_num,
                        column="date",
                        error="Date is required",
                        value=row.get('date')
                    ))
                else:
                    pd.to_datetime(row['date']).date()
            except Exception:
                errors.append(CSVValidationError(
                    row=row_num,
                    column="date",
                    error="Invalid date format. Use YYYY-MM-DD",
                    value=row.get('date')
                ))
            
            # Validate start_time
            try:
                if pd.isna(row.get('start_time')):
                    errors.append(CSVValidationError(
                        row=row_num,
                        column="start_time",
                        error="Start time is required",
                        value=row.get('start_time')
                    ))
                else:
                    pd.to_datetime(row['start_time'])
            except Exception:
                errors.append(CSVValidationError(
                    row=row_num,
                    column="start_time",
                    error="Invalid start time format. Use YYYY-MM-DD HH:MM:SS",
                    value=row.get('start_time')
                ))
            
            # Validate end_time
            try:
                if pd.isna(row.get('end_time')):
                    errors.append(CSVValidationError(
                        row=row_num,
                        column="end_time",
                        error="End time is required",
                        value=row.get('end_time')
                    ))
                else:
                    end_time = pd.to_datetime(row['end_time'])
                    start_time = pd.to_datetime(row['start_time'])
                    if end_time <= start_time:
                        errors.append(CSVValidationError(
                            row=row_num,
                            column="end_time",
                            error="End time must be after start time",
                            value=row.get('end_time')
                        ))
            except Exception:
                errors.append(CSVValidationError(
                    row=row_num,
                    column="end_time",
                    error="Invalid end time format. Use YYYY-MM-DD HH:MM:SS",
                    value=row.get('end_time')
                ))
            
            # Validate break_duration if provided
            if not pd.isna(row.get('break_duration')):
                try:
                    break_duration = int(row['break_duration'])
                    if break_duration < 0:
                        errors.append(CSVValidationError(
                            row=row_num,
                            column="break_duration",
                            error="Break duration cannot be negative",
                            value=row.get('break_duration')
                        ))
                except ValueError:
                    errors.append(CSVValidationError(
                        row=row_num,
                        column="break_duration",
                        error="Break duration must be a number (minutes)",
                        value=row.get('break_duration')
                    ))
        
        return len(errors) == 0, errors, df
        
    except Exception as e:
        errors.append(CSVValidationError(
            row=0,
            column="file",
            error=f"Error reading CSV file: {str(e)}",
            value=None
        ))
        return False, errors, None


def create_csv_preview(df: pd.DataFrame, max_rows: int = 5) -> CSVPreviewData:
    """Create preview data from DataFrame"""
    sample_rows = []
    
    # Get first few rows for preview
    preview_df = df.head(max_rows)
    
    for _, row in preview_df.iterrows():
        row_dict = {}
        for col in df.columns:
            value = row[col]
            # Convert pandas types to JSON-serializable types
            if pd.isna(value):
                row_dict[col] = None
            elif isinstance(value, (pd.Timestamp, datetime)):
                row_dict[col] = value.isoformat()
            else:
                row_dict[col] = str(value)
        sample_rows.append(row_dict)
    
    return CSVPreviewData(
        headers=df.columns.tolist(),
        sample_rows=sample_rows,
        total_rows=len(df),
        validation_errors=[]
    )


def process_csv_import(
    import_job_id: int,
    file_path: str,
    db: Session
) -> ImportJob:
    """Process CSV import in background"""
    import_job = db.query(ImportJob).filter(ImportJob.id == import_job_id).first()
    if not import_job:
        raise ValueError(f"Import job {import_job_id} not found")
    
    try:
        # Update job status
        import_job.status = ImportJobStatus.PROCESSING
        import_job.started_at = datetime.utcnow()
        db.commit()
        
        # Validate and read CSV
        is_valid, errors, df = validate_csv_format(file_path)
        
        if not is_valid or df is None:
            import_job.status = ImportJobStatus.FAILED
            import_job.error_details = [error.dict() for error in errors]
            import_job.completed_at = datetime.utcnow()
            db.commit()
            return import_job
        
        import_job.total_records = len(df)
        successful_records = 0
        failed_records = 0
        error_details = []
        
        # Process each row
        for index, row in df.iterrows():
            try:
                employee_id = int(row['employee_id'])
                date_val = pd.to_datetime(row['date']).date()
                start_time = pd.to_datetime(row['start_time'])
                end_time = pd.to_datetime(row['end_time'])
                break_duration = int(row.get('break_duration', 0)) if not pd.isna(row.get('break_duration')) else 0
                
                # Validate timesheet entry
                entry_errors = validate_timesheet_entry(
                    start_time, end_time, break_duration, employee_id, db
                )
                
                if entry_errors:
                    failed_records += 1
                    error_details.append({
                        'row': index + 2,
                        'errors': entry_errors
                    })
                    continue
                
                # Calculate hours and create entry
                hours_worked = calculate_hours_worked(start_time, end_time, break_duration)
                
                timesheet_entry = TimesheetEntry(
                    employee_id=employee_id,
                    date=date_val,
                    start_time=start_time,
                    end_time=end_time,
                    break_duration=break_duration,
                    hours_worked=hours_worked,
                    project_code=row.get('project_code') if not pd.isna(row.get('project_code')) else None,
                    task_description=row.get('task_description') if not pd.isna(row.get('task_description')) else None,
                    entry_method="csv_import",
                    notes=row.get('notes') if not pd.isna(row.get('notes')) else None
                )
                
                db.add(timesheet_entry)
                successful_records += 1
                
            except Exception as e:
                failed_records += 1
                error_details.append({
                    'row': index + 2,
                    'error': str(e)
                })
        
        # Update job with results
        import_job.processed_records = successful_records + failed_records
        import_job.successful_records = successful_records
        import_job.failed_records = failed_records
        import_job.error_details = error_details
        import_job.status = ImportJobStatus.COMPLETED
        import_job.completed_at = datetime.utcnow()
        
        if successful_records > 0:
            db.commit()
        else:
            db.rollback()
            import_job.status = ImportJobStatus.FAILED
            db.commit()
        
    except Exception as e:
        import_job.status = ImportJobStatus.FAILED
        import_job.error_details = [{'error': str(e)}]
        import_job.completed_at = datetime.utcnow()
        db.commit()
        logger.error(f"CSV import failed for job {import_job_id}: {e}")
    
    return import_job


def generate_csv_template() -> str:
    """Generate a CSV template file"""
    template_data = {
        'employee_id': [1, 2],
        'date': ['2024-01-15', '2024-01-15'],
        'start_time': ['2024-01-15 09:00:00', '2024-01-15 08:30:00'],
        'end_time': ['2024-01-15 17:00:00', '2024-01-15 16:30:00'],
        'break_duration': [60, 30],
        'project_code': ['PROJ001', 'PROJ002'],
        'task_description': ['Development work', 'Testing'],
        'notes': ['Regular workday', 'Half day']
    }
    
    df = pd.DataFrame(template_data)
    
    # Create template file
    template_path = os.path.join(settings.UPLOAD_DIR, 'timesheet_template.csv')
    os.makedirs(os.path.dirname(template_path), exist_ok=True)
    df.to_csv(template_path, index=False)
    
    return template_path
