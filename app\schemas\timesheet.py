from pydantic import BaseModel
from datetime import datetime, date
from decimal import Decimal
from typing import Optional

class TimesheetBase(BaseModel):
    date: date
    hours_worked: Decimal
    status: str

class TimesheetCreate(TimesheetBase):
    pass

class TimesheetUpdate(TimesheetBase):
    pass

class TimesheetResponse(TimesheetBase):
    id: int
    employee_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class LeaveRequestBase(BaseModel):
    start_date: date
    end_date: date
    leave_type: str
    notes: Optional[str] = None

class LeaveRequestCreate(LeaveRequestBase):
    employee_id: int

class LeaveRequestUpdate(BaseModel):
    status: str

class LeaveRequestResponse(LeaveRequestBase):
    id: int
    employee_id: int
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
