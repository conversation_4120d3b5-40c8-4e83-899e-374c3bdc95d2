from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.v1.endpoints import (
    auth,
    users,
    employees,
    timesheet,
    payroll,
    reviews,
    recruitment,
    documents,
    benefits
)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(
    users.router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["users"]
)

app.include_router(
    auth.router,
    prefix=f"{settings.API_V1_STR}/auth",
    tags=["authentication"]
)

app.include_router(
    employees.router,
    prefix=f"{settings.API_V1_STR}/employees",
    tags=["employees"]
)

app.include_router(
    timesheet.router,
    prefix=f"{settings.API_V1_STR}/employees",
    tags=["timesheet"]
)

app.include_router(
    payroll.router,
    prefix=f"{settings.API_V1_STR}/employees",
    tags=["payroll"]
)

app.include_router(
    reviews.router,
    prefix=f"{settings.API_V1_STR}/employees",
    tags=["reviews"]
)

app.include_router(
    recruitment.router,
    prefix=f"{settings.API_V1_STR}",
    tags=["recruitment"]
)

app.include_router(
    documents.router,
    prefix=f"{settings.API_V1_STR}/employees",
    tags=["documents"]
)

app.include_router(
    benefits.router,
    prefix=f"{settings.API_V1_STR}",
    tags=["benefits"]
)

@app.get("/")
async def root():
    return {"message": "Welcome to HR Portal API"}
