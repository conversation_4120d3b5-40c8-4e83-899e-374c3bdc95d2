# HR Portal API

A comprehensive RESTful web service designed to digitize and streamline human resources operations.

## Features

- Authentication & Security (JWT-based)
- Employee Management
- Time & Attendance
- Payroll & Compensation
- Performance Reviews
- Recruitment & Onboarding
- Document Management
- Benefits Administration

## Prerequisites

- Python 3.8+
- Virtual Environment

## Setup

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows:
     ```bash
     .\.venv\Scripts\activate
     ```
   - Unix/MacOS:
     ```bash
     source .venv/bin/activate
     ```

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Create a `.env` file with the following variables:
   ```
   DATABASE_URL=sqlite:///./hr_portal.db
   SECRET_KEY=your-secret-key-for-jwt
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   ```

6. Initialize the database:
   ```bash
   alembic upgrade head
   ```

7. Run the application:
   ```bash
   uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000`

API documentation will be available at `http://localhost:8000/docs`

## Project Structure

```
app/
├── alembic/              # Database migrations
├── app/
│   ├── api/             # API endpoints
│   ├── core/            # Core functionality
│   ├── models/          # SQLAlchemy models
│   └── schemas/         # Pydantic schemas
├── tests/               # Test files
├── .env                 # Environment variables
├── alembic.ini          # Alembic configuration
└── main.py             # Application entry point
```

## Development

1. Create a new branch for your feature
2. Make your changes
3. Run tests
4. Create a pull request

## Testing

Run tests with pytest:
```bash
pytest
```

## API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
