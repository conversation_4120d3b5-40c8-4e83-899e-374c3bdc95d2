import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime, date, timedelta
import tempfile
import os

from app.main import app
from app.core.database import get_db, Base
from app.models.employee import Employee, TimesheetEntry, PunchSession, ImportJob
from app.models.user import User
from app.core.security import get_password_hash

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_timesheet.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


@pytest.fixture
def test_user_and_employee():
    """Create a test user and employee"""
    db = TestingSessionLocal()
    
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        first_name="Test",
        last_name="User",
        is_active=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    
    # Create test employee
    employee = Employee(
        user_id=user.id,
        employee_id="EMP001",
        department="IT",
        position="Developer",
        hire_date=date.today()
    )
    db.add(employee)
    db.commit()
    db.refresh(employee)
    
    db.close()
    return user, employee


@pytest.fixture
def auth_headers(test_user_and_employee):
    """Get authentication headers for test user"""
    user, employee = test_user_and_employee
    
    # Login to get token
    response = client.post(
        "/api/v1/auth/login",
        data={"username": user.email, "password": "testpassword"}
    )
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


class TestPunchClockSystem:
    """Test punch clock functionality"""
    
    def test_punch_in_success(self, auth_headers, test_user_and_employee):
        """Test successful punch in"""
        user, employee = test_user_and_employee
        
        response = client.post(
            "/api/v1/employees/punch-in",
            json={
                "location": "Office",
                "device_info": "Chrome Browser",
                "notes": "Starting work"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["employee_id"] == employee.id
        assert data["is_active"] is True
        assert data["location"] == "Office"
    
    def test_punch_out_success(self, auth_headers, test_user_and_employee):
        """Test successful punch out"""
        user, employee = test_user_and_employee
        
        # First punch in
        client.post(
            "/api/v1/employees/punch-in",
            json={"notes": "Starting work"},
            headers=auth_headers
        )
        
        # Then punch out
        response = client.post(
            "/api/v1/employees/punch-out",
            json={"notes": "End of work"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_active"] is False
        assert data["punch_out_time"] is not None
    
    def test_punch_in_when_already_active(self, auth_headers, test_user_and_employee):
        """Test punch in when already having an active session"""
        # First punch in
        client.post(
            "/api/v1/employees/punch-in",
            json={"notes": "Starting work"},
            headers=auth_headers
        )
        
        # Try to punch in again
        response = client.post(
            "/api/v1/employees/punch-in",
            json={"notes": "Starting work again"},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "already has an active session" in response.json()["detail"]


class TestEnhancedManualEntry:
    """Test enhanced manual entry functionality"""
    
    def test_create_timesheet_entry(self, auth_headers, test_user_and_employee):
        """Test creating a detailed timesheet entry"""
        user, employee = test_user_and_employee
        
        entry_data = {
            "date": "2024-08-14",
            "start_time": "2024-08-14T09:00:00",
            "end_time": "2024-08-14T17:00:00",
            "break_duration": 60,
            "project_code": "PROJ001",
            "task_description": "Development work",
            "notes": "Regular workday",
            "entry_method": "manual"
        }
        
        response = client.post(
            f"/api/v1/employees/{employee.id}/timesheet-entries",
            json=entry_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["employee_id"] == employee.id
        assert data["hours_worked"] == "7.00"  # 8 hours - 1 hour break
        assert data["project_code"] == "PROJ001"
    
    def test_bulk_timesheet_creation(self, auth_headers, test_user_and_employee):
        """Test bulk timesheet creation"""
        user, employee = test_user_and_employee
        
        bulk_data = {
            "entries": [
                {
                    "date": "2024-08-14",
                    "start_time": "2024-08-14T09:00:00",
                    "end_time": "2024-08-14T17:00:00",
                    "break_duration": 60,
                    "project_code": "PROJ001"
                },
                {
                    "date": "2024-08-15",
                    "start_time": "2024-08-15T09:00:00",
                    "end_time": "2024-08-15T17:00:00",
                    "break_duration": 60,
                    "project_code": "PROJ002"
                }
            ]
        }
        
        response = client.post(
            f"/api/v1/employees/{employee.id}/timesheets/bulk",
            json=bulk_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["successful_entries"] == 2
        assert data["failed_entries"] == 0
        assert len(data["created_ids"]) == 2
    
    def test_date_range_creation(self, auth_headers, test_user_and_employee):
        """Test date range timesheet creation"""
        user, employee = test_user_and_employee
        
        range_data = {
            "start_date": "2024-08-19",  # Monday
            "end_date": "2024-08-23",    # Friday
            "daily_start_time": "2024-08-19T09:00:00",
            "daily_end_time": "2024-08-19T17:00:00",
            "break_duration": 60,
            "project_code": "PROJ001",
            "exclude_weekends": True
        }
        
        response = client.post(
            f"/api/v1/employees/{employee.id}/timesheets/range",
            json=range_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["successful_entries"] == 5  # Monday to Friday
        assert data["failed_entries"] == 0


class TestCSVUploadSystem:
    """Test CSV upload functionality"""
    
    def test_csv_template_download(self):
        """Test downloading CSV template"""
        response = client.get("/api/v1/employees/timesheets/csv-template")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
    
    def test_csv_validation(self, auth_headers):
        """Test CSV validation"""
        # Create a test CSV file
        csv_content = """employee_id,date,start_time,end_time,break_duration,project_code
1,2024-08-14,2024-08-14 09:00:00,2024-08-14 17:00:00,60,PROJ001
2,2024-08-14,2024-08-14 08:30:00,2024-08-14 16:30:00,30,PROJ002"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                response = client.post(
                    "/api/v1/employees/timesheets/validate-csv",
                    files={"file": ("test.csv", f, "text/csv")},
                    headers=auth_headers
                )
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_rows"] == 2
            assert len(data["headers"]) == 6
            assert len(data["sample_rows"]) == 2
        
        finally:
            os.unlink(temp_path)


class TestBusinessLogic:
    """Test business logic and validation"""
    
    def test_timesheet_summary(self, auth_headers, test_user_and_employee):
        """Test timesheet summary generation"""
        user, employee = test_user_and_employee
        
        # Create some test entries first
        db = TestingSessionLocal()
        entry = TimesheetEntry(
            employee_id=employee.id,
            date=date.today(),
            start_time=datetime.now().replace(hour=9, minute=0),
            end_time=datetime.now().replace(hour=17, minute=0),
            break_duration=60,
            hours_worked=7.0,
            entry_method="manual",
            status="submitted"
        )
        db.add(entry)
        db.commit()
        db.close()
        
        response = client.get(
            f"/api/v1/employees/{employee.id}/timesheet-summary",
            params={
                "start_date": date.today().isoformat(),
                "end_date": date.today().isoformat()
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["employee_id"] == employee.id
        assert data["total_hours"] == "7.00"
        assert data["days_worked"] == 1
    
    def test_overlap_detection(self, auth_headers, test_user_and_employee):
        """Test overlap detection in timesheet entries"""
        user, employee = test_user_and_employee
        
        # Create first entry
        entry_data = {
            "date": "2024-08-14",
            "start_time": "2024-08-14T09:00:00",
            "end_time": "2024-08-14T17:00:00",
            "break_duration": 60,
            "entry_method": "manual"
        }
        
        response1 = client.post(
            f"/api/v1/employees/{employee.id}/timesheet-entries",
            json=entry_data,
            headers=auth_headers
        )
        assert response1.status_code == 200
        
        # Try to create overlapping entry
        overlapping_data = {
            "date": "2024-08-14",
            "start_time": "2024-08-14T16:00:00",  # Overlaps with previous entry
            "end_time": "2024-08-14T20:00:00",
            "break_duration": 30,
            "entry_method": "manual"
        }
        
        response2 = client.post(
            f"/api/v1/employees/{employee.id}/timesheet-entries",
            json=overlapping_data,
            headers=auth_headers
        )
        
        assert response2.status_code == 400
        assert "overlap" in response2.json()["detail"]["errors"][0].lower()


# Cleanup
def teardown_module():
    """Clean up test database"""
    if os.path.exists("./test_timesheet.db"):
        os.remove("./test_timesheet.db")
