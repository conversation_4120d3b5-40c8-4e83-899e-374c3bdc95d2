from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, List
import json
import asyncio
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time punch clock updates"""
    
    def __init__(self):
        self.active_connections: Dict[int, List[WebSocket]] = {}
        self.employee_sessions: Dict[int, Dict] = {}
    
    async def connect(self, websocket: WebSocket, employee_id: int):
        """Accept a new WebSocket connection for an employee"""
        await websocket.accept()
        
        if employee_id not in self.active_connections:
            self.active_connections[employee_id] = []
        
        self.active_connections[employee_id].append(websocket)
        logger.info(f"Employee {employee_id} connected via WebSocket")
        
        # Send current session status
        await self.send_session_status(employee_id)
    
    def disconnect(self, websocket: WebSocket, employee_id: int):
        """Remove a WebSocket connection"""
        if employee_id in self.active_connections:
            if websocket in self.active_connections[employee_id]:
                self.active_connections[employee_id].remove(websocket)
            
            # Clean up empty connection lists
            if not self.active_connections[employee_id]:
                del self.active_connections[employee_id]
        
        logger.info(f"Employee {employee_id} disconnected from WebSocket")
    
    async def send_personal_message(self, message: dict, employee_id: int):
        """Send a message to all connections for a specific employee"""
        if employee_id in self.active_connections:
            disconnected_connections = []
            
            for connection in self.active_connections[employee_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to employee {employee_id}: {e}")
                    disconnected_connections.append(connection)
            
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.disconnect(connection, employee_id)
    
    async def send_session_status(self, employee_id: int):
        """Send current session status to employee"""
        session_data = self.employee_sessions.get(employee_id, {})
        message = {
            "type": "session_status",
            "data": session_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(message, employee_id)
    
    async def update_session(self, employee_id: int, session_data: dict):
        """Update session data and notify connected clients"""
        self.employee_sessions[employee_id] = session_data
        
        message = {
            "type": "session_update",
            "data": session_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(message, employee_id)
    
    async def notify_punch_in(self, employee_id: int, session_data: dict):
        """Notify about punch in event"""
        message = {
            "type": "punch_in",
            "data": session_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(message, employee_id)
    
    async def notify_punch_out(self, employee_id: int, session_data: dict):
        """Notify about punch out event"""
        message = {
            "type": "punch_out",
            "data": session_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_personal_message(message, employee_id)
    
    async def send_heartbeat(self):
        """Send heartbeat to all connected clients"""
        message = {
            "type": "heartbeat",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        for employee_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, employee_id)
    
    async def start_heartbeat(self, interval: int = 30):
        """Start periodic heartbeat"""
        while True:
            await asyncio.sleep(interval)
            await self.send_heartbeat()


# Global connection manager instance
manager = ConnectionManager()


async def handle_websocket_connection(websocket: WebSocket, employee_id: int):
    """Handle WebSocket connection lifecycle"""
    await manager.connect(websocket, employee_id)
    
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.utcnow().isoformat()
                }))
            elif message.get("type") == "request_status":
                await manager.send_session_status(employee_id)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket, employee_id)
    except Exception as e:
        logger.error(f"WebSocket error for employee {employee_id}: {e}")
        manager.disconnect(websocket, employee_id)
