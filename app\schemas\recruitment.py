from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class JobBase(BaseModel):
    title: str
    department: str
    description: str
    requirements: str
    status: str = "open"

class JobCreate(JobBase):
    pass

class JobResponse(JobBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class JobApplicationBase(BaseModel):
    applicant_name: str
    applicant_email: EmailStr

class JobApplicationCreate(JobApplicationBase):
    pass

class JobApplicationResponse(JobApplicationBase):
    id: int
    job_id: int
    status: str
    resume_path: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ApplicantResponse(JobApplicationResponse):
    job: JobResponse

    class Config:
        from_attributes = True
