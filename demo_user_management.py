#!/usr/bin/env python3
"""
Demonstration script showing all user management endpoints in action.
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000/api/v1"

def demo_user_management():
    """Demonstrate all user management endpoints."""
    print("🚀 HR Portal User Management API Demo")
    print("=" * 50)
    
    # Step 1: Login as admin
    print("\n1️⃣ Logging in as admin...")
    login_response = requests.post(
        f"{BASE_URL}/users/token",
        data={"username": "<EMAIL>", "password": "adminpassword123"}
    )
    
    if login_response.status_code != 200:
        print("❌ Admin login failed. Please ensure admin user exists.")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Admin login successful!")
    
    # Step 2: Create a new user
    print("\n2️⃣ Creating a new user...")
    new_user = {
        "email": f"demo.user.{int(time.time())}@example.com",
        "password": "demopassword123",
        "first_name": "Demo",
        "last_name": "User"
    }
    
    create_response = requests.post(f"{BASE_URL}/users/users/", json=new_user)
    if create_response.status_code == 200:
        user_data = create_response.json()
        user_id = user_data["id"]
        print(f"✅ User created successfully! ID: {user_id}, Email: {user_data['email']}")
    else:
        print(f"❌ User creation failed: {create_response.text}")
        return
    
    # Step 3: Get all users
    print("\n3️⃣ Retrieving all users...")
    users_response = requests.get(f"{BASE_URL}/users/users/", headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()
        print(f"✅ Retrieved {len(users)} users")
        for user in users[-3:]:  # Show last 3 users
            print(f"   - {user['email']} (ID: {user['id']}, Active: {user['is_active']})")
    else:
        print(f"❌ Failed to get users: {users_response.text}")
    
    # Step 4: Get single user
    print(f"\n4️⃣ Getting user details for ID {user_id}...")
    user_response = requests.get(f"{BASE_URL}/users/users/{user_id}", headers=headers)
    if user_response.status_code == 200:
        user = user_response.json()
        print(f"✅ User details: {user['first_name']} {user['last_name']} ({user['email']})")
    else:
        print(f"❌ Failed to get user: {user_response.text}")
    
    # Step 5: Update user
    print(f"\n5️⃣ Updating user {user_id}...")
    update_data = {
        "first_name": "Updated Demo",
        "last_name": "User Modified"
    }
    update_response = requests.put(
        f"{BASE_URL}/users/users/{user_id}", 
        json=update_data, 
        headers=headers
    )
    if update_response.status_code == 200:
        updated_user = update_response.json()
        print(f"✅ User updated: {updated_user['first_name']} {updated_user['last_name']}")
    else:
        print(f"❌ Failed to update user: {update_response.text}")
    
    # Step 6: Soft delete user
    print(f"\n6️⃣ Soft deleting user {user_id}...")
    delete_response = requests.delete(f"{BASE_URL}/users/users/{user_id}", headers=headers)
    if delete_response.status_code == 200:
        result = delete_response.json()
        print(f"✅ {result['message']}")
    else:
        print(f"❌ Failed to delete user: {delete_response.text}")
    
    # Step 7: Verify user is deactivated
    print(f"\n7️⃣ Verifying user {user_id} is deactivated...")
    verify_response = requests.get(f"{BASE_URL}/users/users/{user_id}", headers=headers)
    if verify_response.status_code == 200:
        user = verify_response.json()
        if not user['is_active']:
            print("✅ User is correctly deactivated")
        else:
            print("❌ User should be deactivated but is still active")
    
    # Step 8: Reactivate user
    print(f"\n8️⃣ Reactivating user {user_id}...")
    reactivate_response = requests.patch(
        f"{BASE_URL}/users/users/{user_id}/reactivate", 
        headers=headers
    )
    if reactivate_response.status_code == 200:
        user = reactivate_response.json()
        print(f"✅ User reactivated: {user['email']} (Active: {user['is_active']})")
    else:
        print(f"❌ Failed to reactivate user: {reactivate_response.text}")
    
    # Step 9: Test search functionality
    print(f"\n9️⃣ Testing search functionality...")
    search_response = requests.get(
        f"{BASE_URL}/users/users/", 
        params={"search": "Demo"}, 
        headers=headers
    )
    if search_response.status_code == 200:
        search_results = search_response.json()
        print(f"✅ Search for 'Demo' returned {len(search_results)} results")
    else:
        print(f"❌ Search failed: {search_response.text}")
    
    # Step 10: Clean up - hard delete the demo user
    print(f"\n🔟 Cleaning up - hard deleting demo user {user_id}...")
    hard_delete_response = requests.delete(
        f"{BASE_URL}/users/users/{user_id}/hard", 
        headers=headers
    )
    if hard_delete_response.status_code == 200:
        result = hard_delete_response.json()
        print(f"✅ {result['message']}")
    else:
        print(f"❌ Failed to hard delete user: {hard_delete_response.text}")
    
    print("\n" + "=" * 50)
    print("🎉 User Management API Demo Completed!")
    print("\n📋 Summary of tested endpoints:")
    print("   ✅ POST /users/token (Login)")
    print("   ✅ POST /users/users/ (Create User)")
    print("   ✅ GET /users/users/ (List Users)")
    print("   ✅ GET /users/users/{id} (Get User)")
    print("   ✅ PUT /users/users/{id} (Update User)")
    print("   ✅ DELETE /users/users/{id} (Soft Delete)")
    print("   ✅ PATCH /users/users/{id}/reactivate (Reactivate)")
    print("   ✅ DELETE /users/users/{id}/hard (Hard Delete)")
    print("   ✅ Search functionality")
    print("\n🔒 Security features verified:")
    print("   ✅ JWT Authentication")
    print("   ✅ Authorization checks")
    print("   ✅ Superuser-only operations")
    print("   ✅ Email uniqueness validation")
    print("   ✅ Password hashing")

if __name__ == "__main__":
    demo_user_management()
