#!/usr/bin/env python3
"""
Test script to verify that the SQLAlchemy relationship issues have been resolved.
"""

import sys
import os
sys.path.append('.')

def test_model_imports():
    """Test that all models can be imported without errors."""
    try:
        from app.models.user import User
        from app.models.employee import (
            Employee, Review, Timesheet, LeaveRequest, 
            Document, Job, JobApplication, Benefit, EmployeeBenefit
        )
        print("✓ All models imported successfully")
        return True
    except Exception as e:
        print(f"✗ Error importing models: {e}")
        return False

def test_database_creation():
    """Test that database tables can be created without relationship errors."""
    try:
        from app.core.database import Base, engine
        from app.models.user import User
        from app.models.employee import (
            Employee, Review, Timesheet, LeaveRequest, 
            Document, Job, JobApplication, Benefit, EmployeeBenefit
        )
        
        # This will fail if there are relationship issues
        Base.metadata.create_all(bind=engine)
        print("✓ Database tables created successfully")
        return True
    except Exception as e:
        print(f"✗ Error creating database tables: {e}")
        return False

def test_relationship_queries():
    """Test that relationship queries work without ambiguous foreign key errors."""
    try:
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.employee import Employee, Review
        
        db = SessionLocal()
        try:
            # Test basic queries that would trigger relationship loading
            users = db.query(User).all()
            employees = db.query(Employee).all()
            reviews = db.query(Review).all()
            
            print(f"✓ Database queries successful:")
            print(f"  - Users: {len(users)}")
            print(f"  - Employees: {len(employees)}")
            print(f"  - Reviews: {len(reviews)}")
            return True
        finally:
            db.close()
    except Exception as e:
        print(f"✗ Error with relationship queries: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing SQLAlchemy relationship fixes...")
    print("=" * 50)
    
    tests = [
        ("Model Imports", test_model_imports),
        ("Database Creation", test_database_creation),
        ("Relationship Queries", test_relationship_queries),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The SQLAlchemy relationship issues have been resolved.")
        return True
    else:
        print("❌ Some tests failed. There may still be relationship issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
