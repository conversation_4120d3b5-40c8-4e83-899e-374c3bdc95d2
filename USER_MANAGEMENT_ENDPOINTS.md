# User Management Endpoints Documentation

This document describes the complete user management API endpoints implemented in the HR Portal.

## Overview

All user management endpoints require authentication (except user creation) and follow RESTful conventions. The API uses JWT tokens for authentication and implements proper authorization checks.

## Authentication

Most endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

Get a token by logging in at `POST /api/v1/users/token`

## Endpoints

### 1. Create User
- **Route**: `POST /api/v1/users/users/`
- **Authentication**: Not required
- **Description**: Create a new user account
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "securepassword",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>"
  }
  ```
- **Response**: User object with ID and timestamps
- **Status Codes**: 
  - 200: Success
  - 400: Email already registered

### 2. Get All Users
- **Route**: `GET /api/v1/users/users/`
- **Authentication**: Required
- **Description**: Retrieve all users with pagination and filtering
- **Query Parameters**:
  - `skip` (int): Number of records to skip (default: 0)
  - `limit` (int): Maximum records to return (default: 100)
  - `search` (str): Search in first_name, last_name, or email
  - `is_active` (bool): Filter by active status
- **Response**: Array of user objects
- **Status Codes**: 
  - 200: Success
  - 401: Unauthorized

### 3. Get Single User
- **Route**: `GET /api/v1/users/users/{user_id}`
- **Authentication**: Required
- **Description**: Retrieve a specific user by ID
- **Path Parameters**:
  - `user_id` (int): User ID
- **Response**: User object
- **Status Codes**: 
  - 200: Success
  - 401: Unauthorized
  - 404: User not found

### 4. Update User
- **Route**: `PUT /api/v1/users/users/{user_id}`
- **Authentication**: Required
- **Description**: Update user information
- **Authorization**: Users can only update their own information unless they are superusers
- **Path Parameters**:
  - `user_id` (int): User ID
- **Request Body** (all fields optional):
  ```json
  {
    "email": "<EMAIL>",
    "first_name": "NewFirst",
    "last_name": "NewLast",
    "password": "newpassword"
  }
  ```
- **Response**: Updated user object
- **Status Codes**: 
  - 200: Success
  - 400: Email already registered
  - 401: Unauthorized
  - 403: Not authorized to update this user
  - 404: User not found

### 5. Soft Delete User
- **Route**: `DELETE /api/v1/users/users/{user_id}`
- **Authentication**: Required (Superuser only)
- **Description**: Deactivate a user (soft delete)
- **Authorization**: Only superusers can delete users
- **Path Parameters**:
  - `user_id` (int): User ID
- **Response**: 
  ```json
  {
    "message": "User <EMAIL> has been deactivated successfully"
  }
  ```
- **Status Codes**: 
  - 200: Success
  - 400: User already inactive or trying to delete self
  - 401: Unauthorized
  - 403: Only superusers can delete users
  - 404: User not found

### 6. Hard Delete User
- **Route**: `DELETE /api/v1/users/users/{user_id}/hard`
- **Authentication**: Required (Superuser only)
- **Description**: Permanently delete a user from database
- **Authorization**: Only superusers can hard delete users
- **Warning**: This action is irreversible
- **Path Parameters**:
  - `user_id` (int): User ID
- **Response**: 
  ```json
  {
    "message": "User <EMAIL> has been permanently deleted"
  }
  ```
- **Status Codes**: 
  - 200: Success
  - 400: Trying to delete self
  - 401: Unauthorized
  - 403: Only superusers can permanently delete users
  - 404: User not found

### 7. Reactivate User
- **Route**: `PATCH /api/v1/users/users/{user_id}/reactivate`
- **Authentication**: Required (Superuser only)
- **Description**: Reactivate a deactivated user
- **Authorization**: Only superusers can reactivate users
- **Path Parameters**:
  - `user_id` (int): User ID
- **Response**: Reactivated user object
- **Status Codes**: 
  - 200: Success
  - 400: User already active
  - 401: Unauthorized
  - 403: Only superusers can reactivate users
  - 404: User not found

### 8. Login
- **Route**: `POST /api/v1/users/token`
- **Authentication**: Not required
- **Description**: Login and get access token
- **Request Body** (form data):
  ```
  username: <EMAIL>
  password: userpassword
  ```
- **Response**: 
  ```json
  {
    "access_token": "jwt_token_here",
    "token_type": "bearer"
  }
  ```
- **Status Codes**: 
  - 200: Success
  - 401: Incorrect email or password

## Security Features

1. **JWT Authentication**: Secure token-based authentication
2. **Password Hashing**: Passwords are hashed using bcrypt
3. **Authorization Checks**: Users can only modify their own data unless they're superusers
4. **Soft Delete**: Users are deactivated rather than permanently deleted by default
5. **Self-Protection**: Users cannot delete their own accounts
6. **Email Uniqueness**: Prevents duplicate email addresses

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:
- 400: Bad Request (validation errors, business logic violations)
- 401: Unauthorized (missing or invalid token)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (resource doesn't exist)
- 500: Internal Server Error (unexpected server errors)

## Usage Examples

### Create and manage a user:
```bash
# 1. Create user
curl -X POST "http://localhost:8000/api/v1/users/users/" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","first_name":"Test","last_name":"User"}'

# 2. Login
curl -X POST "http://localhost:8000/api/v1/users/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword123"

# 3. Get users (with token)
curl -X GET "http://localhost:8000/api/v1/users/users/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 4. Update user
curl -X PUT "http://localhost:8000/api/v1/users/users/1" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"first_name":"Updated"}'

# 5. Soft delete user (superuser only)
curl -X DELETE "http://localhost:8000/api/v1/users/users/1" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Testing

Use the provided test script `test_user_endpoints.py` to verify all endpoints work correctly.

The API documentation is also available at: http://localhost:8000/docs
