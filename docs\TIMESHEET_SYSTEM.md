# Enhanced Timesheet System Documentation

## Overview

The enhanced timesheet system supports three distinct data input methods:
1. **Real-time Punch Clock Integration** - WebSocket-based real-time tracking
2. **Enhanced Manual Data Entry** - Bulk operations and advanced forms
3. **CSV File Upload** - Bulk import with validation and preview

## Architecture

### Database Models

#### TimesheetEntry
Enhanced timesheet entry with detailed time tracking:
- `start_time` and `end_time` for precise time tracking
- `break_duration` in minutes
- `project_code` and `task_description` for project tracking
- `entry_method` to track how the entry was created
- Automatic `hours_worked` calculation

#### PunchSession
Active punch clock sessions for real-time tracking:
- `punch_in_time` and `punch_out_time`
- `is_active` flag for session status
- `location` and `device_info` for validation
- Automatic conversion to TimesheetEntry on punch out

#### ImportJob
Track CSV import jobs and their status:
- File processing status and progress
- Error details and validation results
- Preview data for user confirmation

### API Endpoints

## 1. Punch Clock System

### WebSocket Connection
```
WS /api/v1/employees/ws/punch-clock/{employee_id}
```
Real-time connection for punch clock updates.

**Message Types:**
- `session_status` - Current session information
- `session_update` - Session state changes
- `punch_in` - Punch in notification
- `punch_out` - Punch out notification
- `heartbeat` - Keep-alive messages

### Punch In
```
POST /api/v1/employees/punch-in
```

**Request Body:**
```json
{
  "location": "Office",
  "device_info": "Chrome Browser",
  "notes": "Starting work"
}
```

**Response:**
```json
{
  "id": 1,
  "employee_id": 123,
  "punch_in_time": "2024-08-14T09:00:00",
  "is_active": true,
  "location": "Office"
}
```

### Punch Out
```
POST /api/v1/employees/punch-out
```

**Request Body:**
```json
{
  "notes": "End of work"
}
```

### Get Active Sessions
```
GET /api/v1/employees/active-sessions
```
Returns all currently active punch sessions (admin/manager only).

### Get Punch History
```
GET /api/v1/employees/{employee_id}/punch-history
```
Returns punch history for an employee with pagination.

## 2. Enhanced Manual Entry

### Create Detailed Entry
```
POST /api/v1/employees/{employee_id}/timesheet-entries
```

**Request Body:**
```json
{
  "date": "2024-08-14",
  "start_time": "2024-08-14T09:00:00",
  "end_time": "2024-08-14T17:00:00",
  "break_duration": 60,
  "project_code": "PROJ001",
  "task_description": "Development work",
  "notes": "Regular workday",
  "entry_method": "manual"
}
```

### Bulk Entry Creation
```
POST /api/v1/employees/{employee_id}/timesheets/bulk
```

**Request Body:**
```json
{
  "entries": [
    {
      "date": "2024-08-14",
      "start_time": "2024-08-14T09:00:00",
      "end_time": "2024-08-14T17:00:00",
      "break_duration": 60,
      "project_code": "PROJ001"
    }
  ]
}
```

### Weekly Timesheet Update
```
PUT /api/v1/employees/{employee_id}/timesheets/week
```
Update or create entries for an entire week.

### Date Range Creation
```
POST /api/v1/employees/{employee_id}/timesheets/range
```

**Request Body:**
```json
{
  "start_date": "2024-08-19",
  "end_date": "2024-08-23",
  "daily_start_time": "2024-08-19T09:00:00",
  "daily_end_time": "2024-08-19T17:00:00",
  "break_duration": 60,
  "project_code": "PROJ001",
  "exclude_weekends": true
}
```

## 3. CSV Upload System

### Upload CSV File
```
POST /api/v1/employees/timesheets/upload-csv
```

**Form Data:**
- `file`: CSV file (multipart/form-data)

**Response:**
```json
{
  "job_id": 1,
  "message": "File uploaded successfully. Processing started.",
  "preview": null
}
```

### Validate CSV
```
POST /api/v1/employees/timesheets/validate-csv
```
Validate CSV format and return preview without importing.

**Response:**
```json
{
  "headers": ["employee_id", "date", "start_time", "end_time"],
  "sample_rows": [
    {
      "employee_id": "1",
      "date": "2024-08-14",
      "start_time": "2024-08-14 09:00:00",
      "end_time": "2024-08-14 17:00:00"
    }
  ],
  "total_rows": 100,
  "validation_errors": []
}
```

### Get Import Job Status
```
GET /api/v1/employees/timesheets/import-jobs/{job_id}
```

### Download CSV Template
```
GET /api/v1/employees/timesheets/csv-template
```
Download a CSV template with sample data.

## CSV Format

### Required Columns
- `employee_id` - Employee ID (integer)
- `date` - Date in YYYY-MM-DD format
- `start_time` - Start time in YYYY-MM-DD HH:MM:SS format
- `end_time` - End time in YYYY-MM-DD HH:MM:SS format

### Optional Columns
- `break_duration` - Break duration in minutes (integer)
- `project_code` - Project code (string)
- `task_description` - Task description (string)
- `notes` - Additional notes (string)

### Example CSV
```csv
employee_id,date,start_time,end_time,break_duration,project_code,task_description,notes
1,2024-08-14,2024-08-14 09:00:00,2024-08-14 17:00:00,60,PROJ001,Development work,Regular workday
2,2024-08-14,2024-08-14 08:30:00,2024-08-14 16:30:00,30,PROJ002,Testing,Half day
```

## Business Logic & Validation

### Overlap Detection
The system automatically detects overlapping time entries for the same employee on the same date.

### Time Calculations
- Hours worked = (end_time - start_time) - break_duration
- Automatic overtime calculation (over 40 hours per week)
- Daily maximum validation (configurable, default 24 hours)

### Anomaly Detection
The system can detect:
- Excessive hours (>12 hours per day)
- Very short shifts (<1 hour)
- Late night work (after 11 PM or before 5 AM)
- Weekend work
- Work gaps (missing days)

### Status Management
- `submitted` - Entry submitted for approval
- `approved` - Entry approved by manager
- `rejected` - Entry rejected with reason

## WebSocket Integration

### Client Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/employees/ws/punch-clock/123');

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch(message.type) {
        case 'punch_in':
            console.log('Punched in:', message.data);
            break;
        case 'punch_out':
            console.log('Punched out:', message.data);
            break;
        case 'session_status':
            console.log('Session status:', message.data);
            break;
    }
};

// Send ping to keep connection alive
ws.send(JSON.stringify({type: 'ping'}));
```

### Real-time Updates
- Automatic session status updates
- Live hour calculations
- Connection heartbeat every 30 seconds
- Automatic reconnection handling

## Configuration

### Environment Variables
```env
# File upload settings
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_CSV_EXTENSIONS=.csv,.xlsx,.xls

# WebSocket settings
WEBSOCKET_HEARTBEAT_INTERVAL=30
MAX_ACTIVE_SESSIONS_PER_USER=1
```

## Security Considerations

### Authentication
- All endpoints require JWT authentication
- Employee association validation
- Role-based access control for admin functions

### File Upload Security
- File type validation
- File size limits
- Secure file storage
- Temporary file cleanup

### Data Validation
- Input sanitization
- SQL injection prevention
- XSS protection
- Rate limiting on file uploads

## Performance Optimizations

### Database Indexes
- Employee + date composite indexes
- Status-based indexes
- Time-based indexes for queries

### Background Processing
- Asynchronous CSV processing
- Automatic cleanup of stale sessions
- Batch operations for bulk imports

### Caching
- Session state caching
- Frequently accessed data caching
- WebSocket connection pooling

## Error Handling

### Validation Errors
Detailed error messages with field-level validation:
```json
{
  "errors": [
    {
      "field": "start_time",
      "error": "Start time must be before end time",
      "value": "2024-08-14T18:00:00"
    }
  ]
}
```

### Import Errors
CSV import errors with row-level details:
```json
{
  "successful_records": 95,
  "failed_records": 5,
  "error_details": [
    {
      "row": 10,
      "errors": ["Invalid date format"]
    }
  ]
}
```

## Testing

Run the enhanced timesheet tests:
```bash
pytest tests/test_timesheet_enhanced.py -v
```

Test coverage includes:
- Punch clock operations
- Manual entry validation
- CSV upload and validation
- Business logic and anomaly detection
- WebSocket functionality
- Error handling scenarios
