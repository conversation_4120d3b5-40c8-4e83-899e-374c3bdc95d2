from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from decimal import Decimal

class Deduction(BaseModel):
    name: str
    amount: Decimal
    type: str

class PayrollResponse(BaseModel):
    employee_id: int
    salary: Decimal
    pay_rate: str
    currency: str
    tax_rate: float
    deductions: List[Deduction]

    class Config:
        from_attributes = True

class PayslipResponse(BaseModel):
    id: int
    employee_id: int
    period_start: date
    period_end: date
    base_salary: Decimal
    gross_pay: Decimal
    net_pay: Decimal
    deductions: List[Deduction]
    created_at: datetime

    class Config:
        from_attributes = True

class TaxFormResponse(BaseModel):
    id: int
    employee_id: int
    year: int
    form_type: str  # W2, 1099, etc.
    status: str
    generated_at: datetime
    download_url: Optional[str]

    class Config:
        from_attributes = True
