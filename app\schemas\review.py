from pydantic import BaseModel
from typing import Optional
from datetime import datetime, date

class ReviewBase(BaseModel):
    review_date: date
    reviewer_id: int
    performance_score: int
    comments: str
    status: str

class ReviewCreate(ReviewBase):
    pass

class ReviewUpdate(BaseModel):
    performance_score: Optional[int] = None
    comments: Optional[str] = None
    status: Optional[str] = None

class ReviewResponse(ReviewBase):
    id: int
    employee_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class GoalBase(BaseModel):
    title: str
    description: str
    target_date: date
    status: str

class GoalCreate(GoalBase):
    pass

class GoalResponse(GoalBase):
    id: int
    employee_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
