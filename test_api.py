#!/usr/bin/env python3
"""
Test script to verify the API endpoint that was originally failing.
"""

import requests
import json

def test_user_creation():
    """Test the user creation endpoint that was originally failing."""
    url = "http://127.0.0.1:8000/api/v1/users/users/"
    
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        print("Testing user creation endpoint...")
        response = requests.post(url, json=user_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ User creation successful! The SQLAlchemy relationship error has been resolved.")
            return True
        elif response.status_code == 400 and "already registered" in response.text:
            print("✓ User already exists, but no SQLAlchemy errors! The relationship issue has been resolved.")
            return True
        else:
            print(f"✗ Unexpected response: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the server. Make sure it's running on http://127.0.0.1:8000")
        return False
    except Exception as e:
        print(f"✗ Error testing API: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint to verify server is running."""
    try:
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code == 200:
            print("✓ Server is running and accessible")
            return True
        else:
            print(f"✗ Server returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error connecting to server: {e}")
        return False

if __name__ == "__main__":
    print("Testing API endpoints...")
    print("=" * 50)
    
    if test_root_endpoint():
        test_user_creation()
    else:
        print("Cannot test user creation - server is not accessible")
