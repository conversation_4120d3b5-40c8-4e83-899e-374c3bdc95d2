from pydantic import BaseModel, validator, Field
from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any
from enum import Enum

class TimesheetBase(BaseModel):
    date: date
    hours_worked: Decimal
    status: str

class TimesheetCreate(TimesheetBase):
    pass

class TimesheetUpdate(TimesheetBase):
    pass

class TimesheetResponse(TimesheetBase):
    id: int
    employee_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class LeaveRequestBase(BaseModel):
    start_date: date
    end_date: date
    leave_type: str
    notes: Optional[str] = None

class LeaveRequestCreate(LeaveRequestBase):
    employee_id: int

class LeaveRequestUpdate(BaseModel):
    status: str

class LeaveRequestResponse(LeaveRequestBase):
    id: int
    employee_id: int
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Enhanced Timesheet Schemas for Multiple Input Methods

class EntryMethod(str, Enum):
    PUNCH_CLOCK = "punch_clock"
    MANUAL = "manual"
    CSV_IMPORT = "csv_import"


class TimesheetStatus(str, Enum):
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"


# Enhanced Timesheet Entry Schemas
class TimesheetEntryBase(BaseModel):
    date: date
    start_time: datetime
    end_time: datetime
    break_duration: Optional[int] = 0  # minutes
    project_code: Optional[str] = None
    task_description: Optional[str] = None
    notes: Optional[str] = None


class TimesheetEntryCreate(TimesheetEntryBase):
    entry_method: EntryMethod = EntryMethod.MANUAL


class TimesheetEntryUpdate(BaseModel):
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    break_duration: Optional[int] = None
    project_code: Optional[str] = None
    task_description: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[TimesheetStatus] = None


class TimesheetEntryResponse(TimesheetEntryBase):
    id: int
    employee_id: int
    hours_worked: Decimal
    entry_method: EntryMethod
    status: TimesheetStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Punch Clock Schemas
class PunchInRequest(BaseModel):
    location: Optional[str] = None
    device_info: Optional[str] = None
    notes: Optional[str] = None


class PunchOutRequest(BaseModel):
    notes: Optional[str] = None


class PunchSessionResponse(BaseModel):
    id: int
    employee_id: int
    punch_in_time: datetime
    punch_out_time: Optional[datetime] = None
    is_active: bool
    location: Optional[str] = None
    device_info: Optional[str] = None
    session_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Bulk Entry Schemas
class BulkTimesheetEntry(BaseModel):
    date: date
    start_time: datetime
    end_time: datetime
    break_duration: Optional[int] = 0
    project_code: Optional[str] = None
    task_description: Optional[str] = None
    notes: Optional[str] = None


class BulkTimesheetCreate(BaseModel):
    entries: List[BulkTimesheetEntry]


class WeeklyTimesheetEntry(BaseModel):
    date: date
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    break_duration: Optional[int] = 0
    project_code: Optional[str] = None
    task_description: Optional[str] = None
    notes: Optional[str] = None


class WeeklyTimesheetUpdate(BaseModel):
    entries: List[WeeklyTimesheetEntry]


class DateRangeTimesheetCreate(BaseModel):
    start_date: date
    end_date: date
    daily_start_time: datetime
    daily_end_time: datetime
    break_duration: Optional[int] = 0
    project_code: Optional[str] = None
    task_description: Optional[str] = None
    notes: Optional[str] = None
    exclude_weekends: bool = True


# CSV Import Schemas
class CSVValidationError(BaseModel):
    row: int
    column: str
    error: str
    value: Any


class CSVPreviewData(BaseModel):
    headers: List[str]
    sample_rows: List[Dict[str, Any]]
    total_rows: int
    validation_errors: List[CSVValidationError]


class ImportJobStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ImportJobResponse(BaseModel):
    id: int
    employee_id: int
    filename: str
    total_records: int
    processed_records: int
    successful_records: int
    failed_records: int
    status: ImportJobStatus
    error_details: Optional[List[Dict[str, Any]]] = None
    preview_data: Optional[CSVPreviewData] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CSVUploadResponse(BaseModel):
    job_id: int
    message: str
    preview: Optional[CSVPreviewData] = None


# Validation and Response Schemas
class TimesheetValidationError(BaseModel):
    field: str
    error: str
    value: Any


class BulkOperationResult(BaseModel):
    successful_entries: int
    failed_entries: int
    errors: List[TimesheetValidationError]
    created_ids: List[int]


class ActiveSessionsResponse(BaseModel):
    active_sessions: List[PunchSessionResponse]
    total_active: int


class PunchHistoryResponse(BaseModel):
    sessions: List[PunchSessionResponse]
    total_sessions: int
    total_hours: Decimal
