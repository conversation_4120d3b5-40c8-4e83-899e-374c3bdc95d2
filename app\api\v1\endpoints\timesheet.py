from fastapi import APIRout<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, date
import os
import aiofiles
from app.core.database import get_db
from app.core.auth import get_current_user
from app.core.websocket_manager import manager, handle_websocket_connection
from app.core.timesheet_utils import (
    calculate_hours_worked,
    validate_punch_session,
    create_timesheet_from_session,
    auto_punch_out_stale_sessions,
    validate_timesheet_entry,
    generate_date_range
)
from app.core.csv_processor import (
    validate_csv_format,
    create_csv_preview,
    process_csv_import,
    generate_csv_template
)
from app.core.config import settings
from app.services.timesheet_service import TimesheetService
from app.models.employee import (
    Timesheet,
    LeaveRequest,
    TimesheetEntry,
    PunchSession,
    ImportJob
)
from app.schemas.timesheet import (
    TimesheetCreate,
    TimesheetUpdate,
    TimesheetResponse,
    LeaveRequestCreate,
    LeaveRequestUpdate,
    LeaveRequestResponse,
    # New schemas
    PunchInRequest,
    PunchOutRequest,
    PunchSessionResponse,
    TimesheetEntryResponse,
    ActiveSessionsResponse,
    PunchHistoryResponse,
    # Manual entry schemas
    TimesheetEntryCreate,
    TimesheetEntryUpdate,
    BulkTimesheetCreate,
    WeeklyTimesheetUpdate,
    DateRangeTimesheetCreate,
    BulkOperationResult,
    # CSV import schemas
    CSVUploadResponse,
    ImportJobResponse,
    CSVPreviewData
)

router = APIRouter()

# Timesheet endpoints
@router.get("/{employee_id}/timesheets", response_model=List[TimesheetResponse])
def get_employee_timesheets(
    employee_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    timesheets = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id
    ).offset(skip).limit(limit).all()
    return timesheets

@router.post("/{employee_id}/timesheets", response_model=TimesheetResponse)
def create_timesheet(
    employee_id: int,
    timesheet: TimesheetCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_timesheet = Timesheet(**timesheet.dict(), employee_id=employee_id)
    db.add(db_timesheet)
    db.commit()
    db.refresh(db_timesheet)
    return db_timesheet

@router.get("/{employee_id}/timesheets/{timesheet_id}", response_model=TimesheetResponse)
def get_timesheet(
    employee_id: int,
    timesheet_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    timesheet = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id,
        Timesheet.id == timesheet_id
    ).first()
    if not timesheet:
        raise HTTPException(status_code=404, detail="Timesheet not found")
    return timesheet

@router.put("/{employee_id}/timesheets/{timesheet_id}", response_model=TimesheetResponse)
def update_timesheet(
    employee_id: int,
    timesheet_id: int,
    timesheet_update: TimesheetUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_timesheet = db.query(Timesheet).filter(
        Timesheet.employee_id == employee_id,
        Timesheet.id == timesheet_id
    ).first()
    if not db_timesheet:
        raise HTTPException(status_code=404, detail="Timesheet not found")
    
    for field, value in timesheet_update.dict().items():
        setattr(db_timesheet, field, value)
    
    db.commit()
    db.refresh(db_timesheet)
    return db_timesheet

# Leave request endpoints
@router.post("/leave/request", response_model=LeaveRequestResponse)
def create_leave_request(
    leave_request: LeaveRequestCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_leave_request = LeaveRequest(**leave_request.dict())
    db.add(db_leave_request)
    db.commit()
    db.refresh(db_leave_request)
    return db_leave_request

@router.get("/leave/requests", response_model=List[LeaveRequestResponse])
def get_leave_requests(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if user has admin/manager privileges here
    leave_requests = db.query(LeaveRequest).offset(skip).limit(limit).all()
    return leave_requests

@router.patch("/leave/requests/{request_id}", response_model=LeaveRequestResponse)
def update_leave_request(
    request_id: int,
    leave_request: LeaveRequestUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if user has admin/manager privileges here
    db_leave_request = db.query(LeaveRequest).filter(
        LeaveRequest.id == request_id
    ).first()
    if not db_leave_request:
        raise HTTPException(status_code=404, detail="Leave request not found")
    
    for field, value in leave_request.dict(exclude_unset=True).items():
        setattr(db_leave_request, field, value)
    
    db.commit()
    db.refresh(db_leave_request)
    return db_leave_request


# WebSocket endpoint for real-time punch clock
@router.websocket("/ws/punch-clock/{employee_id}")
async def websocket_punch_clock(websocket: WebSocket, employee_id: int):
    """WebSocket endpoint for real-time punch clock updates"""
    await handle_websocket_connection(websocket, employee_id)


# Punch Clock Endpoints
@router.post("/punch-in", response_model=PunchSessionResponse)
async def punch_in(
    punch_data: PunchInRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Punch in - start a work session"""
    employee_id = current_user.employee.id if current_user.employee else None
    if not employee_id:
        raise HTTPException(status_code=400, detail="User is not associated with an employee")

    # Clean up stale sessions in background
    background_tasks.add_task(auto_punch_out_stale_sessions, db)

    # Validate punch in
    can_punch, error_msg, active_session = validate_punch_session(employee_id, db)
    if active_session:
        raise HTTPException(status_code=400, detail="Employee already has an active session")

    # Create new punch session
    punch_session = PunchSession(
        employee_id=employee_id,
        punch_in_time=datetime.utcnow(),
        location=punch_data.location,
        device_info=punch_data.device_info,
        session_notes=punch_data.notes,
        is_active=True
    )

    db.add(punch_session)
    db.commit()
    db.refresh(punch_session)

    # Notify via WebSocket
    session_data = {
        "id": punch_session.id,
        "punch_in_time": punch_session.punch_in_time.isoformat(),
        "is_active": punch_session.is_active,
        "location": punch_session.location
    }
    await manager.notify_punch_in(employee_id, session_data)

    return punch_session


@router.post("/punch-out", response_model=PunchSessionResponse)
async def punch_out(
    punch_data: PunchOutRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Punch out - end the active work session"""
    employee_id = current_user.employee.id if current_user.employee else None
    if not employee_id:
        raise HTTPException(status_code=400, detail="User is not associated with an employee")

    # Find active session
    active_session = db.query(PunchSession).filter(
        PunchSession.employee_id == employee_id,
        PunchSession.is_active == True
    ).first()

    if not active_session:
        raise HTTPException(status_code=400, detail="No active session found")

    # Update session
    active_session.punch_out_time = datetime.utcnow()
    active_session.is_active = False
    if punch_data.notes:
        active_session.session_notes = (active_session.session_notes or "") + f" | {punch_data.notes}"

    # Create timesheet entry
    timesheet_entry = create_timesheet_from_session(active_session, db)

    db.commit()
    db.refresh(active_session)

    # Notify via WebSocket
    session_data = {
        "id": active_session.id,
        "punch_in_time": active_session.punch_in_time.isoformat(),
        "punch_out_time": active_session.punch_out_time.isoformat(),
        "is_active": active_session.is_active,
        "hours_worked": float(timesheet_entry.hours_worked)
    }
    await manager.notify_punch_out(employee_id, session_data)

    return active_session


@router.get("/active-sessions", response_model=ActiveSessionsResponse)
def get_active_sessions(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get all active punch sessions (admin/manager only)"""
    # TODO: Add role-based access control
    active_sessions = db.query(PunchSession).filter(
        PunchSession.is_active == True
    ).all()

    return ActiveSessionsResponse(
        active_sessions=active_sessions,
        total_active=len(active_sessions)
    )


@router.get("/{employee_id}/punch-history", response_model=PunchHistoryResponse)
def get_punch_history(
    employee_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get punch history for an employee"""
    sessions = db.query(PunchSession).filter(
        PunchSession.employee_id == employee_id
    ).order_by(PunchSession.created_at.desc()).offset(skip).limit(limit).all()

    # Calculate total hours from completed sessions
    completed_sessions = [s for s in sessions if not s.is_active and s.punch_out_time]
    total_hours = sum(
        calculate_hours_worked(s.punch_in_time, s.punch_out_time)
        for s in completed_sessions
    )

    return PunchHistoryResponse(
        sessions=sessions,
        total_sessions=len(sessions),
        total_hours=total_hours
    )


# Enhanced Manual Entry Endpoints

@router.post("/{employee_id}/timesheet-entries", response_model=TimesheetEntryResponse)
def create_timesheet_entry(
    employee_id: int,
    entry: TimesheetEntryCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new detailed timesheet entry"""
    # Validate the entry
    errors = validate_timesheet_entry(
        entry.start_time,
        entry.end_time,
        entry.break_duration,
        employee_id,
        db
    )

    if errors:
        raise HTTPException(status_code=400, detail={"errors": errors})

    # Calculate hours worked
    hours_worked = calculate_hours_worked(
        entry.start_time,
        entry.end_time,
        entry.break_duration
    )

    # Create timesheet entry
    db_entry = TimesheetEntry(
        employee_id=employee_id,
        date=entry.date,
        start_time=entry.start_time,
        end_time=entry.end_time,
        break_duration=entry.break_duration,
        hours_worked=hours_worked,
        project_code=entry.project_code,
        task_description=entry.task_description,
        entry_method=entry.entry_method,
        notes=entry.notes
    )

    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)

    return db_entry


@router.post("/{employee_id}/timesheets/bulk", response_model=BulkOperationResult)
def create_bulk_timesheets(
    employee_id: int,
    bulk_data: BulkTimesheetCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create multiple timesheet entries at once"""
    successful_entries = 0
    failed_entries = 0
    errors = []
    created_ids = []

    for i, entry in enumerate(bulk_data.entries):
        try:
            # Validate each entry
            entry_errors = validate_timesheet_entry(
                entry.start_time,
                entry.end_time,
                entry.break_duration,
                employee_id,
                db
            )

            if entry_errors:
                failed_entries += 1
                errors.extend([
                    {"field": f"entry_{i}", "error": error, "value": None}
                    for error in entry_errors
                ])
                continue

            # Calculate hours and create entry
            hours_worked = calculate_hours_worked(
                entry.start_time,
                entry.end_time,
                entry.break_duration
            )

            db_entry = TimesheetEntry(
                employee_id=employee_id,
                date=entry.date,
                start_time=entry.start_time,
                end_time=entry.end_time,
                break_duration=entry.break_duration,
                hours_worked=hours_worked,
                project_code=entry.project_code,
                task_description=entry.task_description,
                entry_method="manual",
                notes=entry.notes
            )

            db.add(db_entry)
            db.flush()  # Get the ID without committing
            created_ids.append(db_entry.id)
            successful_entries += 1

        except Exception as e:
            failed_entries += 1
            errors.append({
                "field": f"entry_{i}",
                "error": str(e),
                "value": None
            })

    if successful_entries > 0:
        db.commit()
    else:
        db.rollback()

    return BulkOperationResult(
        successful_entries=successful_entries,
        failed_entries=failed_entries,
        errors=errors,
        created_ids=created_ids
    )


@router.put("/{employee_id}/timesheets/week", response_model=BulkOperationResult)
def update_weekly_timesheet(
    employee_id: int,
    week_data: WeeklyTimesheetUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update an entire week's timesheet entries"""
    successful_entries = 0
    failed_entries = 0
    errors = []
    created_ids = []

    for i, entry in enumerate(week_data.entries):
        try:
            if not entry.start_time or not entry.end_time:
                # Skip entries without times
                continue

            # Check if entry exists for this date
            existing_entry = db.query(TimesheetEntry).filter(
                TimesheetEntry.employee_id == employee_id,
                TimesheetEntry.date == entry.date
            ).first()

            # Validate the entry
            entry_errors = validate_timesheet_entry(
                entry.start_time,
                entry.end_time,
                entry.break_duration,
                employee_id,
                db,
                exclude_entry_id=existing_entry.id if existing_entry else None
            )

            if entry_errors:
                failed_entries += 1
                errors.extend([
                    {"field": f"entry_{i}", "error": error, "value": None}
                    for error in entry_errors
                ])
                continue

            # Calculate hours
            hours_worked = calculate_hours_worked(
                entry.start_time,
                entry.end_time,
                entry.break_duration
            )

            if existing_entry:
                # Update existing entry
                existing_entry.start_time = entry.start_time
                existing_entry.end_time = entry.end_time
                existing_entry.break_duration = entry.break_duration
                existing_entry.hours_worked = hours_worked
                existing_entry.project_code = entry.project_code
                existing_entry.task_description = entry.task_description
                existing_entry.notes = entry.notes
                created_ids.append(existing_entry.id)
            else:
                # Create new entry
                new_entry = TimesheetEntry(
                    employee_id=employee_id,
                    date=entry.date,
                    start_time=entry.start_time,
                    end_time=entry.end_time,
                    break_duration=entry.break_duration,
                    hours_worked=hours_worked,
                    project_code=entry.project_code,
                    task_description=entry.task_description,
                    entry_method="manual",
                    notes=entry.notes
                )
                db.add(new_entry)
                db.flush()
                created_ids.append(new_entry.id)

            successful_entries += 1

        except Exception as e:
            failed_entries += 1
            errors.append({
                "field": f"entry_{i}",
                "error": str(e),
                "value": None
            })

    if successful_entries > 0:
        db.commit()
    else:
        db.rollback()

    return BulkOperationResult(
        successful_entries=successful_entries,
        failed_entries=failed_entries,
        errors=errors,
        created_ids=created_ids
    )


@router.post("/{employee_id}/timesheets/range", response_model=BulkOperationResult)
def create_date_range_timesheets(
    employee_id: int,
    range_data: DateRangeTimesheetCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create timesheet entries for a date range"""
    # Generate dates in the range
    dates = generate_date_range(
        range_data.start_date,
        range_data.end_date,
        range_data.exclude_weekends
    )

    successful_entries = 0
    failed_entries = 0
    errors = []
    created_ids = []

    for date_entry in dates:
        try:
            # Create datetime objects for this date
            start_time = datetime.combine(date_entry, range_data.daily_start_time.time())
            end_time = datetime.combine(date_entry, range_data.daily_end_time.time())

            # Validate the entry
            entry_errors = validate_timesheet_entry(
                start_time,
                end_time,
                range_data.break_duration,
                employee_id,
                db
            )

            if entry_errors:
                failed_entries += 1
                errors.extend([
                    {"field": f"date_{date_entry}", "error": error, "value": None}
                    for error in entry_errors
                ])
                continue

            # Calculate hours
            hours_worked = calculate_hours_worked(
                start_time,
                end_time,
                range_data.break_duration
            )

            # Create entry
            db_entry = TimesheetEntry(
                employee_id=employee_id,
                date=date_entry,
                start_time=start_time,
                end_time=end_time,
                break_duration=range_data.break_duration,
                hours_worked=hours_worked,
                project_code=range_data.project_code,
                task_description=range_data.task_description,
                entry_method="manual",
                notes=range_data.notes
            )

            db.add(db_entry)
            db.flush()
            created_ids.append(db_entry.id)
            successful_entries += 1

        except Exception as e:
            failed_entries += 1
            errors.append({
                "field": f"date_{date_entry}",
                "error": str(e),
                "value": None
            })

    if successful_entries > 0:
        db.commit()
    else:
        db.rollback()

    return BulkOperationResult(
        successful_entries=successful_entries,
        failed_entries=failed_entries,
        errors=errors,
        created_ids=created_ids
    )


# CSV Upload Endpoints

@router.post("/timesheets/upload-csv", response_model=CSVUploadResponse)
async def upload_csv_timesheet(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Upload CSV file for bulk timesheet import"""
    employee_id = current_user.employee.id if current_user.employee else None
    if not employee_id:
        raise HTTPException(status_code=400, detail="User is not associated with an employee")

    # Validate file type
    if not file.filename.lower().endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only CSV, XLSX, and XLS files are allowed"
        )

    # Check file size
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Maximum size is {settings.MAX_FILE_SIZE} bytes"
        )

    # Create upload directory if it doesn't exist
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

    # Save uploaded file
    file_path = os.path.join(settings.UPLOAD_DIR, f"{employee_id}_{datetime.utcnow().timestamp()}_{file.filename}")

    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    # Create import job
    import_job = ImportJob(
        employee_id=employee_id,
        filename=file.filename,
        file_path=file_path,
        status="pending"
    )

    db.add(import_job)
    db.commit()
    db.refresh(import_job)

    # Start background processing
    background_tasks.add_task(process_csv_import, import_job.id, file_path, db)

    return CSVUploadResponse(
        job_id=import_job.id,
        message="File uploaded successfully. Processing started.",
        preview=None
    )


@router.post("/timesheets/validate-csv", response_model=CSVPreviewData)
async def validate_csv_timesheet(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Validate CSV file and return preview without importing"""
    # Validate file type
    if not file.filename.lower().endswith('.csv'):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only CSV files are allowed for validation"
        )

    # Save temporary file
    temp_path = os.path.join(settings.UPLOAD_DIR, f"temp_{datetime.utcnow().timestamp()}_{file.filename}")
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

    async with aiofiles.open(temp_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    try:
        # Validate and create preview
        is_valid, errors, df = validate_csv_format(temp_path)

        if df is not None:
            preview = create_csv_preview(df)
            preview.validation_errors = errors
        else:
            preview = CSVPreviewData(
                headers=[],
                sample_rows=[],
                total_rows=0,
                validation_errors=errors
            )

        return preview

    finally:
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)


@router.get("/timesheets/import-jobs/{job_id}", response_model=ImportJobResponse)
def get_import_job_status(
    job_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get the status of a CSV import job"""
    import_job = db.query(ImportJob).filter(ImportJob.id == job_id).first()

    if not import_job:
        raise HTTPException(status_code=404, detail="Import job not found")

    # Check if user has access to this job
    employee_id = current_user.employee.id if current_user.employee else None
    if import_job.employee_id != employee_id:
        raise HTTPException(status_code=403, detail="Access denied")

    return import_job


@router.get("/timesheets/csv-template")
def download_csv_template():
    """Download CSV template for timesheet import"""
    template_path = generate_csv_template()

    return FileResponse(
        path=template_path,
        filename="timesheet_template.csv",
        media_type="text/csv"
    )


# Business Logic and Validation Endpoints

@router.get("/{employee_id}/timesheet-summary")
def get_timesheet_summary(
    employee_id: int,
    start_date: date,
    end_date: date,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get comprehensive timesheet summary for an employee"""
    service = TimesheetService(db)
    return service.get_employee_timesheet_summary(employee_id, start_date, end_date)


@router.get("/{employee_id}/timesheet-anomalies")
def get_timesheet_anomalies(
    employee_id: int,
    start_date: date,
    end_date: date,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Detect potential anomalies in timesheet data"""
    service = TimesheetService(db)
    return service.detect_timesheet_anomalies(employee_id, start_date, end_date)


@router.get("/{employee_id}/weekly-summary")
def get_weekly_summary(
    employee_id: int,
    week_start: date,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get weekly timesheet summary for an employee"""
    service = TimesheetService(db)
    return service.calculate_weekly_totals(employee_id, week_start)


@router.get("/pending-approvals")
def get_pending_approvals(
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get timesheet entries pending approval (admin/manager only)"""
    # TODO: Add role-based access control
    service = TimesheetService(db)
    return service.get_pending_approvals(limit)


@router.post("/approve-entries")
def approve_timesheet_entries(
    entry_ids: List[int],
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Approve multiple timesheet entries (admin/manager only)"""
    # TODO: Add role-based access control
    approver_id = current_user.employee.id if current_user.employee else None
    if not approver_id:
        raise HTTPException(status_code=400, detail="User is not associated with an employee")

    service = TimesheetService(db)
    return service.approve_timesheet_entries(entry_ids, approver_id)


@router.post("/reject-entries")
def reject_timesheet_entries(
    entry_ids: List[int],
    rejection_reason: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Reject multiple timesheet entries (admin/manager only)"""
    # TODO: Add role-based access control
    approver_id = current_user.employee.id if current_user.employee else None
    if not approver_id:
        raise HTTPException(status_code=400, detail="User is not associated with an employee")

    service = TimesheetService(db)
    return service.reject_timesheet_entries(entry_ids, rejection_reason, approver_id)
