from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.employee import Job, JobApplication
from app.schemas.recruitment import (
    JobCreate,
    JobResponse,
    JobApplicationCreate,
    JobApplicationResponse,
    ApplicantResponse
)

router = APIRouter()

@router.get("/jobs", response_model=List[JobResponse])
def get_jobs(
    skip: int = 0,
    limit: int = 100,
    status: str = "open",
    db: Session = Depends(get_db)
):
    jobs = db.query(Job).filter(
        Job.status == status
    ).offset(skip).limit(limit).all()
    return jobs

@router.post("/jobs", response_model=JobResponse)
def create_job(
    job: JobCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    db_job = Job(**job.dict())
    db.add(db_job)
    db.commit()
    db.refresh(db_job)
    return db_job

@router.get("/jobs/{job_id}", response_model=JobResponse)
def get_job(
    job_id: int,
    db: Session = Depends(get_db)
):
    job = db.query(Job).filter(Job.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job

@router.get("/jobs/{job_id}/applicants", response_model=List[JobApplicationResponse])
def get_job_applicants(
    job_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    applications = db.query(JobApplication).filter(
        JobApplication.job_id == job_id
    ).offset(skip).limit(limit).all()
    return applications

@router.post("/jobs/{job_id}/apply", response_model=JobApplicationResponse)
async def apply_for_job(
    job_id: int,
    application: JobApplicationCreate,
    resume: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    # In a real implementation, save the resume file and store its path
    resume_path = f"resumes/{job_id}_{application.applicant_email}_{resume.filename}"
    
    db_application = JobApplication(
        **application.dict(),
        job_id=job_id,
        resume_path=resume_path,
        status="submitted"
    )
    db.add(db_application)
    db.commit()
    db.refresh(db_application)
    return db_application

@router.get("/applicants/{applicant_id}", response_model=ApplicantResponse)
def get_applicant(
    applicant_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    application = db.query(JobApplication).filter(
        JobApplication.id == applicant_id
    ).first()
    if not application:
        raise HTTPException(status_code=404, detail="Applicant not found")
    return application
