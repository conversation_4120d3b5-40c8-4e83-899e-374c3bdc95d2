#!/usr/bin/env python3
"""
Quick test script to verify authentication endpoints are working
"""
import requests
import json

def test_auth_endpoints():
    base_url = "http://127.0.0.1:8000"
    
    print("Testing authentication endpoints...")
    
    # Test 1: Check if the server is running
    try:
        response = requests.get(f"{base_url}/")
        print(f"✓ Server is running: {response.status_code}")
        print(f"  Response: {response.json()}")
    except requests.exceptions.ConnectionError:
        print("✗ Server is not running. Please start with: uvicorn app.main:app --reload")
        return False
    
    # Test 2: Check OpenAPI docs endpoint
    try:
        response = requests.get(f"{base_url}/docs")
        print(f"✓ Swagger UI accessible: {response.status_code}")
    except Exception as e:
        print(f"✗ Swagger UI error: {e}")
    
    # Test 3: Check the token endpoint
    try:
        # This should return 422 (validation error) since we're not sending credentials
        response = requests.post(f"{base_url}/api/v1/users/token")
        print(f"✓ Token endpoint exists: {response.status_code}")
        if response.status_code == 422:
            print("  Expected validation error (no credentials provided)")
        else:
            print(f"  Response: {response.text}")
    except Exception as e:
        print(f"✗ Token endpoint error: {e}")
    
    # Test 4: Check auth login endpoint
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login")
        print(f"✓ Auth login endpoint exists: {response.status_code}")
        if response.status_code == 422:
            print("  Expected validation error (no credentials provided)")
        else:
            print(f"  Response: {response.text}")
    except Exception as e:
        print(f"✗ Auth login endpoint error: {e}")
    
    # Test 5: Check OpenAPI schema for auth
    try:
        response = requests.get(f"{base_url}/openapi.json")
        if response.status_code == 200:
            openapi_spec = response.json()
            
            # Check if token endpoint is in the spec
            paths = openapi_spec.get("paths", {})
            token_path = "/api/v1/users/token"
            auth_path = "/api/v1/auth/login"
            
            if token_path in paths:
                print(f"✓ Token endpoint found in OpenAPI spec: {token_path}")
            else:
                print(f"✗ Token endpoint NOT found in OpenAPI spec")
            
            if auth_path in paths:
                print(f"✓ Auth login endpoint found in OpenAPI spec: {auth_path}")
            else:
                print(f"✗ Auth login endpoint NOT found in OpenAPI spec")
            
            # Check security schemes
            components = openapi_spec.get("components", {})
            security_schemes = components.get("securitySchemes", {})
            
            print(f"Security schemes found: {list(security_schemes.keys())}")
            
            for scheme_name, scheme_data in security_schemes.items():
                if scheme_data.get("type") == "oauth2":
                    flows = scheme_data.get("flows", {})
                    password_flow = flows.get("password", {})
                    token_url = password_flow.get("tokenUrl", "")
                    print(f"  OAuth2 token URL: {token_url}")
        
    except Exception as e:
        print(f"✗ OpenAPI spec error: {e}")
    
    print("\nTo fix Swagger UI authentication:")
    print("1. Make sure the server is running: uvicorn app.main:app --reload")
    print("2. Go to http://127.0.0.1:8000/docs")
    print("3. Click 'Authorize' button")
    print("4. Use the token endpoint: /api/v1/users/token")
    print("5. Enter username (email) and password")
    
    return True

if __name__ == "__main__":
    test_auth_endpoints()
